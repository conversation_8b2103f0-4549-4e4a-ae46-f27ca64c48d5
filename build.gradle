buildscript {
    dependencies {
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.6.2"
    }
}
plugins {
    id 'org.springframework.boot' version '2.6.2'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'java-library'
}

group = 'com.fykj'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

configurations {
    developmentOnly
    runtimeClasspath {
        extendsFrom developmentOnly
    }
    compileOnly {
        extendsFrom annotationProcessor
    }
}

test {
    useJUnitPlatform()
}

repositories {
    maven {
        //指定要上传的maven私服仓库
        url = "http://demo42.fyxmt.com/nexus/content/repositories/fyxmt/"
        //认证用户和密码
        credentials {
            username 'deployment'
            password 'fengyuntec'
        }
    }
    maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
    mavenCentral()
}

dependencies {
    dependencies { api fileTree(dir:'libs',include:['*.jar'])}
    implementation 'io.minio:minio:8.2.1'
    implementation 'com.squareup.okhttp3:okhttp:3.14.8'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
    implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity5'
    compile 'org.springframework.boot:spring-boot-starter-data-elasticsearch'
    implementation 'fykj.microservice:cache:0.0.19'
    implementation 'fykj.microservice:core:2.1.3'
    compile group: 'org.springframework.security.oauth', name: 'spring-security-oauth2', version: '2.3.6.RELEASE'
    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    compileOnly 'org.projectlombok:lombok'
    implementation 'com.github.jsqlparser:jsqlparser:4.2'
    // developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    api 'com.github.axet:kaptcha:0.0.9'
    api 'com.qiniu:qiniu-java-sdk:7.2.23'
    implementation group: 'com.github.binarywang', name: 'weixin-java-mp', version: '4.3.6.B'
    compile group: 'com.github.binarywang', name: 'weixin-java-miniapp', version: '4.3.6.B'
    compile group: 'fr.opensagres.xdocreport', name: 'xdocreport', version: '2.0.2'
    compile group: 'org.apache.poi', name: 'poi', version: '4.1.2'
    compile group: 'org.apache.poi', name: 'poi-scratchpad', version: '4.1.2'
    compile group: 'org.apache.poi', name: 'poi-ooxml', version: '4.1.2'
    compile group: 'org.apache.poi', name: 'poi-ooxml-schemas', version: '4.1.2'
    compile group: 'org.apache.poi', name: 'ooxml-schemas', version: '1.4'
    compile 'com.deepoove:poi-tl:1.10.5'
    compile group: 'com.github.binarywang', name: 'weixin-java-mp', version: '4.3.9.B'
    compile group: 'org.jsoup', name: 'jsoup', version: '1.15.3'
    compile group: 'commons-configuration', name: 'commons-configuration', version: '1.10'
    compile group: 'org.apache.velocity', name: 'velocity', version: '1.7'
    compile group: 'com.yunpian.sdk', name: 'yunpian-java-sdk', version: '1.2.7'
    compile group: 'cn.hutool', name: 'hutool-all', version: '5.8.12'
    implementation group: 'com.xuxueli', name: 'xxl-job-core', version: '2.3.0'
    api group: 'org.apache.rocketmq', name: 'rocketmq-spring-boot-starter', version: '2.2.2'
    implementation fileTree(dir: 'libs', includes: ['*.jar'])
    implementation 'com.sun.xml.bind:jaxb-impl:2.1.3'
    implementation 'com.alibaba:druid-spring-boot-starter:1.2.17'
    compile group: 'commons-fileupload', name: 'commons-fileupload', version: '1.4'
    implementation group: 'com.itextpdf', name: 'itextpdf', version: '5.4.5'
    implementation group: 'com.itextpdf', name: 'itext-asian', version: '5.2.0'
    implementation 'org.apache.pdfbox:pdfbox:2.0.24'
    implementation 'org.apache.pdfbox:fontbox:2.0.24'
    implementation group: 'org.springframework', name: 'spring-test', version: '5.3.14'
//     implementation 'org.apache.pdfbox:pdfbox:2.0.31'
}

apply plugin: "org.sonarqube"
sonarqube {
    properties {
        property "sonar.projectName", "scaffold"
        property "sonar.projectCode", "org.sonarqube:scaffold"
        property "sonar.host.url", "http://sonar.fyxmt.com"
    }
}
