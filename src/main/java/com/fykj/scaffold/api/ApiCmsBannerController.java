package com.fykj.scaffold.api;

import com.fykj.scaffold.cms.domain.entity.CmsBanner;
import com.fykj.scaffold.cms.service.ICmsBannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import result.JsonResult;

import java.util.List;

/**
 * 轮播图
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2020-11-30
 */
@RestController
@RequestMapping("/api/cms/banner")
@Api(tags = "轮播图接口")
public class ApiCmsBannerController {

    @Autowired
    private ICmsBannerService bannerService;

    @ApiOperation("获取轮播图列表")
    @GetMapping(value = "/list")
    public JsonResult<List<CmsBanner>> getPmBannerList() {
        return new JsonResult<>(bannerService.getBannerList());
    }

}
