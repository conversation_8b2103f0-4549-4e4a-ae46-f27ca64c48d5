package com.fykj.scaffold.zyz.util;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.Resource;
import exception.BusinessException;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class DownloadFileUtil {

    public static void downloadFile (HttpServletResponse response, String filePath){
        Resource template = new ClassPathResource(filePath);
        try {
            InputStream in = template.getStream();
            OutputStream out = response.getOutputStream();
            int length = in.available();
            byte[] data = new byte[length];
            response.setContentLength(length);
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            while (in.read(data) > 0) {
                out.write(data);
            }
            out.write(data);
            out.flush();
            in.close();
            out.close();
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "读取文件失败", e);
        }
    }
}
