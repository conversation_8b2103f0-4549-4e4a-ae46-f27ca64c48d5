package com.fykj.scaffold.zyz.controller;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.support.syslog.AuditLog;
import com.fykj.scaffold.support.utils.DesensitiseUtil;
import com.fykj.scaffold.zyz.domain.dto.AwardImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerCertificateParams;
import com.fykj.scaffold.zyz.service.IZyzVolunteerCertificateService;
import fykj.microservice.cache.support.DictTransUtil;
import fykj.microservice.core.base.BaseController;
import fykj.microservice.core.support.excel.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import result.JsonResult;
import result.Result;
import result.ResultCode;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 志愿者证书关联表
 * <p>
 * 前端控制器
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/admin/zyz/volunteer/certificate")
@Api(tags = " 志愿者证书关联表接口")
public class ZyzVolunteerCertificateController extends BaseController<IZyzVolunteerCertificateService, ZyzVolunteerCertificate, ZyzVolunteerCertificateParams> {

    @ApiOperation("志愿者分页查询")
    @PostMapping("/getPages")
    public JsonResult<IPage<ZyzVolunteerCertificate>> getPages(@RequestBody ZyzVolunteerCertificateParams params) {
        IPage<ZyzVolunteerCertificate> iPage = baseService.getPages(params);
        DictTransUtil.trans(iPage.getRecords());
        DesensitiseUtil.desensitise(iPage.getRecords());
        return new JsonResult<>(iPage);
    }

    //获取志愿者证书列表
    @ApiOperation("获取志愿者证书列表")
    @GetMapping("/getAwardedCertificateList")
    public JsonResult<List<ZyzVolunteerCertificate>> getAwardedCertificateList(Long volunteerId) {
        List<ZyzVolunteerCertificate> list = baseService.getAwardedCertificateList(volunteerId);
        return new JsonResult<>(list);
    }

    /**
     * 模板下载
     */
    @ApiOperation("模板下载")
    @GetMapping("/template")
    public void template(HttpServletResponse response) {
        downloadFile(response, "data/excelTemplate/award-template.xlsx");
    }

    @AuditLog("通过Excel批量导入颁发的志愿者信息")
    @ApiOperation("导入")
    @PostMapping(value = "/dataImport")
    public void dataImport(@RequestParam("excel") MultipartFile excel, @RequestParam("awardCertificateId") Long awardCertificateId) {
        List<AwardImportDto> failureList = baseService.dataImport(excel, awardCertificateId);
        if (CollUtil.isNotEmpty(failureList)) {
            ExcelUtil.fillExcel(failureList, "award_fill_template.xlsx", AwardImportDto.class);
        }
    }

    @AuditLog("导出数据到Excel")
    @PostMapping("/export")
    @ApiOperation("导出数据到Excel")
    public void export(@RequestBody ZyzVolunteerCertificateParams params) {
        List<ZyzVolunteerCertificate> list = baseService.list(params);
        DictTransUtil.trans(list);
        ExcelUtil.fillExcel(list, "award-export.xlsx", ZyzVolunteerCertificate.class);
    }

    @ApiOperation("手动生成证书")
    @GetMapping({"/generateCertificatePdfByIds"})
    public Result generateCertificatePdfByIds(@RequestParam String ids) {
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        boolean result = this.baseService.generateCertificatePdfByIds(idList);
        return result ? OK : new Result(ResultCode.FAIL);
    }
}
