package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzTeamRetrieve;
import com.fykj.scaffold.zyz.domain.params.ZyzTeamRetrieveParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 志愿者团队找回表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-08-08
 */
public interface IZyzTeamRetrieveService extends IBaseService<ZyzTeamRetrieve> {

    /**
     * 团队找回信息核实提交
     * @param teamRetrieve
     */
    void checkSubmit(ZyzTeamRetrieve teamRetrieve);

    /**
     * 找回团队分页查询
     * @param params
     * @return
     */
    IPage<ZyzTeamRetrieve> getPages(ZyzTeamRetrieveParams params);

    /**
     * 获取团队找回list
     * @param params
     * @return
     */
    List<ZyzTeamRetrieve> getList(ZyzTeamRetrieveParams params);

    /**
     * 找回团队批量审核
     * @param teamIds
     * @param remark
     * @param pass
     * @return
     */
    void audit(List<Long> teamIds, String remark, Boolean pass);

    /**
     * 根据id获取团队找回信息
     * @param id
     * @return
     */
    ZyzTeamRetrieve getTeamById(Long id);

    /**
     * 获取待审核团队找回数量
     * @return
     */
    Integer getTeamRetrieveWaitAuditNum();
}

