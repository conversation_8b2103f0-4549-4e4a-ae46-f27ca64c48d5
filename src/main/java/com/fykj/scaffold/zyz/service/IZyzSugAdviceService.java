package com.fykj.scaffold.zyz.service;

import com.fykj.scaffold.zyz.domain.entity.ZyzSugAdvice;
import com.fykj.scaffold.zyz.domain.params.ZyzSugAdviceParams;
import fykj.microservice.core.base.IBaseService;

import java.util.List;

/**
 * 咨询建议-服务类
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
public interface IZyzSugAdviceService extends IBaseService<ZyzSugAdvice> {


    /**
     * 回复
     *
     * @param entity 回复内容
     * @return true/false
     */
    boolean reply(ZyzSugAdvice entity);

    /**
     * 获取当前用户的咨询建议清单
     *
     * @param params 查询参数
     * @return list
     */
    List<ZyzSugAdvice> findMyAdviceList(ZyzSugAdviceParams params);
}

