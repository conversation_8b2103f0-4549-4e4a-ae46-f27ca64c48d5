package com.fykj.scaffold.zyz.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.dynamic.domain.entity.DynamicFieldSnapshot;
import com.fykj.scaffold.dynamic.service.IDynamicFieldSnapshotService;
import com.fykj.scaffold.support.conns.SysMsgTmpCons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.support.utils.TokenUtil;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.ApplyPublicPageDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityApplyPublicExport;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivity;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriod;
import com.fykj.scaffold.zyz.domain.entity.ZyzBlackPublicList;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityApplyPublicParams;
import com.fykj.scaffold.zyz.mapper.ZyzActivityApplyPublicMapper;
import com.fykj.scaffold.zyz.service.IZyzActivityApplyPublicService;
import com.fykj.scaffold.zyz.service.IZyzActivityService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodService;
import com.fykj.scaffold.zyz.service.IZyzBlackPublicListService;
import constants.Mark;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 活动群众报名表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @since 2023-07-31
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzActivityApplyPublicServiceImpl extends BaseServiceImpl<ZyzActivityApplyPublicMapper, ZyzActivityApplyPublic> implements IZyzActivityApplyPublicService {


    @Autowired
    private IZyzActivityTimePeriodService activityTimePeriodService;

    @Autowired
    private IZyzActivityService activityService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;

    @Autowired
    private IZyzBlackPublicListService zyzBlackPublicListService;

    @Autowired
    private IDynamicFieldSnapshotService dynamicFieldSnapshotService;

    @Override
    public void saveActivityForPublic(ZyzActivityApplyPublic entity) {
        if(!IdcardUtil.isValidCard(entity.getCertificateId())){
            throw new BusinessException(ResultCode.FAIL, "填写的身份证号码无效");
        }
        if (lambdaQuery().eq(ZyzActivityApplyPublic::getTimePeriodId, entity.getTimePeriodId())
                .eq(ZyzActivityApplyPublic::getStatus, true)
                .eq(ZyzActivityApplyPublic::getPhone, entity.getPhone())
                .exists()) {
            throw new BusinessException(ResultCode.FAIL, "该群众已报名这个时间段的活动，请勿重复报名");
        }

        if (lambdaQuery().eq(ZyzActivityApplyPublic::getTimePeriodId, entity.getTimePeriodId())
                .eq(ZyzActivityApplyPublic::getStatus, true)
                .eq(ZyzActivityApplyPublic::getCertificateId, entity.getCertificateId())
                .exists()) {
            throw new BusinessException(ResultCode.FAIL, "该群众已报名这个时间段的活动，请勿重复报名");
        }
        ZyzActivityTimePeriod timePeriod = activityTimePeriodService.lambdaQuery().eq(ZyzActivityTimePeriod::getId, entity.getTimePeriodId()).one();
        ZyzActivity activity = activityService.getById(entity.getActivityId());
        //如果招募人数区别控制，则判断条件为群众报名人数，否则大于等于群众招募人数
        if (activity.getRecruitNumDistinguish() && timePeriod.getMassesApplyNum() >= timePeriod.getMassesRecruitNum()) {
            throw new BusinessException(ResultCode.FAIL, "该时间段报名人数已满，请选择其他时间段");
        }
        //如果招募人数不区别控制，则判断条件为总报名人数，否则大于等于总招募人数
        if (!activity.getRecruitNumDistinguish() && timePeriod.getApplyNum() >= timePeriod.getRecruitmentNum()) {
            throw new BusinessException(ResultCode.FAIL, "该时间段报名人数已满，请选择其他时间段");
        }
        //黑名单检查
        ZyzBlackPublicList blackList = zyzBlackPublicListService.getBlackByMobile(entity.getPhone());
        if (blackList != null) {
            throw new BusinessException(ResultCode.FAIL,
                    "因您爽约多次，无法继续报名，限制时间1个月，剩余解除时间"
                    + LocalDateTimeUtil.between(LocalDateTime.now(), blackList.getCancelTime(), ChronoUnit.DAYS)
                    + "天。报名失败，请联系0512-68235283");
        }
        ZyzActivityApplyPublic applyPublic = lambdaQuery()
                .and(x -> x
                        .eq(ZyzActivityApplyPublic::getPhone, entity.getPhone())
                        .or()
                        .eq(ZyzActivityApplyPublic::getCertificateId, entity.getCertificateId()))
                .eq(ZyzActivityApplyPublic::getTimePeriodId, entity.getTimePeriodId())
                .one();
        if (applyPublic == null) {
            //如果是新数据，处理冗余字段
            entity.setStartTime(timePeriod.getStartTime());
            entity.setEndTime(timePeriod.getEndTime());
            entity.setStatus(true);
            entity.setCreator((Long) TokenUtil.getUserId());
            super.save(entity);
        } else {
            //如果是老数据，修改报名状态
            applyPublic.setStatus(true);
            super.updateById(applyPublic);
        }
        //时段表报名人数加1
        activityTimePeriodService.addMassesApply(entity.getTimePeriodId(), 1);
        //发送信息给报名群众
        List<String> phones = new ArrayList<>();
        phones.add(entity.getPhone());
        sendTmpMsg.commonSendByPhones(SysMsgTmpCons.SMT_ACTIVITY_APPLY_PUBLIC, phones, applyPublic == null ? entity.getActivityName() : applyPublic.getActivityName(),  activity.getContactPerson(),activity.getContactPhone(), applyPublic == null ? entity.getId() : applyPublic.getId());
        //发送信息给发布人
        List<Long> activityIds = new ArrayList<>();
        activityIds.add(entity.getActivityId());
        sendTmpMsg.sendToActivityPublisher(SysMsgTmpCons.SMT_ACTIVITY_APPLY_NOTICE, activityIds, "报名", entity.getActivityName(), entity.getName());
    }

    @Override
    public void cancelApply(List<Long> applyPublicIds, Long timePeriodId) {
        if (applyPublicIds.isEmpty()) {
            throw new BusinessException(ResultCode.FAIL, "请选择要取消报名的活动");
        }
        List<ZyzActivityApplyPublic> applyPublicList = new ArrayList<>();
        lambdaQuery().in(ZyzActivityApplyPublic::getId, applyPublicIds).list()
                .forEach(applyPublic -> {
                    if (!applyPublic.getStatus()) {
                        throw new BusinessException(ResultCode.FAIL, "您已取消报名，请勿重复操作！");
                    }
                    if (Objects.nonNull(applyPublic.getEndTime())
                            && applyPublic.getEndTime().isBefore(LocalDateTime.now())) {
                        throw new BusinessException(ResultCode.FAIL, "活动已结束，无法取消报名！");
                    }
                    //修改报名状态
                    applyPublic.setStatus(false);
                    applyPublicList.add(applyPublic);
                    //发送信息给发布人
                    sendTmpMsg.sendToActivityPublisher(SysMsgTmpCons.SMT_ACTIVITY_APPLY_NOTICE,
                            Collections.singletonList(applyPublic.getActivityId()),
                            "取消报名", applyPublic.getActivityName(), applyPublic.getName());
                    //减去报名人数
                    activityTimePeriodService.minusMassesApply(applyPublic.getTimePeriodId(), applyPublicIds.size());
                });
        //批量修改
        super.updateBatchById(applyPublicList);
    }

    @Override
    public IPage<ZyzActivityApplyPublic> pagesForApplyPublic(ZyzActivityApplyPublicParams params) {
        Page<ZyzActivityApplyPublic> page = Page.of(params.getCurrentPage(), params.getPageSize());
        return baseMapper.pageOrListForApplyPublic(page, params);
//        return super.page(params);
    }


    @Override
    public List<ZyzActivityApplyPublicExport> listForApplyPublic(ZyzActivityApplyPublicParams params) {
        List<ZyzActivityApplyPublicExport> res = new ArrayList<>();
        List<ZyzActivityApplyPublic> publicList = baseMapper.pageOrListForApplyPublic(params);
        publicList.forEach(it -> {
            ZyzActivityApplyPublicExport dto = new ZyzActivityApplyPublicExport();
            BeanUtils.copyProperties(it, dto);
            if (it.getStartTime() != null && it.getEndTime() != null) {
                dto.setActivityTimeRange(LocalDateTimeUtil.format(it.getStartTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN)
                        .concat(Mark.LINE).concat(LocalDateTimeUtil.format(it.getEndTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN)));
            }
            res.add(dto);
        });
        return res;
    }

    /**
     * 导出带动态字段的群众报名记录
     * @param params 查询参数
     * @param response HTTP响应对象
     */
    @Override
    public void exportWithDynamicFields(ZyzActivityApplyPublicParams params, HttpServletResponse response) {
        // 1. 校验并获取活动信息
        ZyzActivity activity = validateAndGetActivity(params.getActivityId());
        // 2. 获取动态表单定义
        List<DynamicFieldSnapshot> customFields = getCustomFields(activity.getApplyPublicFormSnapshotId());
        // 3. 获取报名数据
        List<ZyzActivityApplyPublic> publicList = baseMapper.pageOrListForApplyPublic(params);
        // 4. 构建导出数据
        List<Map<String, Object>> dataList = buildExportData(publicList, customFields);
        // 5. 导出Excel
        exportToExcel(response, activity.getName(), dataList);
    }

    /**
     * 校验活动ID并获取活动信息
     */
    private ZyzActivity validateAndGetActivity(Long activityId) {
        return Optional.ofNullable(activityId)
                .map(activityService::getById)
                .orElseThrow(() -> new BusinessException(ResultCode.FAIL, "活动不存在"));
    }

    /**
     * 获取自定义字段列表
     */
    private List<DynamicFieldSnapshot> getCustomFields(Long snapshotId) {
        return dynamicFieldSnapshotService.getBySnapshotIdAndModuleCode(
                snapshotId, ZyzCons.ZYZ_ACTIVITY_PUBLIC_APPLY_MODULE_CODE).stream()
                .filter(field -> !field.getSystemFlag())
                .collect(Collectors.toList());
    }

    /**
     * 构建导出数据
     */
    private List<Map<String, Object>> buildExportData(List<ZyzActivityApplyPublic> publicList,
                                                     List<DynamicFieldSnapshot> customFields) {
        return publicList.stream()
                .map(item -> {
                    Map<String, Object> rowMap = new LinkedHashMap<>();
                    addSystemFields(rowMap, item);
                    Optional.ofNullable(item.getExtraInfo())
                            .ifPresent(extraInfo -> addCustomFields(rowMap, extraInfo, customFields));
                    return rowMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 添加系统固定字段
     */
    private void addSystemFields(Map<String, Object> rowMap, ZyzActivityApplyPublic item) {
        rowMap.put("活动名称", item.getActivityName());
        rowMap.put("活动时段", formatTimeRange(item.getStartTime(), item.getEndTime()));
        rowMap.put("姓名", item.getName());
        rowMap.put("志愿者联系方式", item.getPhone());
        rowMap.put("身份证号", item.getCertificateId());
        rowMap.put("性别", item.getSexText());
        rowMap.put("年龄", item.getAge());
        rowMap.put("是否党员", Objects.isNull(item.getParty())? "" : item.getParty() ? "是" : "否");
        rowMap.put("住址", item.getAddress());
        rowMap.put("报名状态", item.getStatus() ? "已报名" : "已取消报名");
    }

    /**
     * 格式化时间范围
     */
    private String formatTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return Optional.ofNullable(startTime)
                .flatMap(start -> Optional.ofNullable(endTime)
                        .map(end -> LocalDateTimeUtil.format(start, DatePattern.NORM_DATETIME_MINUTE_PATTERN)
                                .concat(Mark.LINE)
                                .concat(LocalDateTimeUtil.format(end, DatePattern.NORM_DATETIME_MINUTE_PATTERN))))
                .orElse("");
    }

    /**
     * 添加自定义动态字段
     */
    private void addCustomFields(Map<String, Object> rowMap, Map<String, String> extraInfo,
                                List<DynamicFieldSnapshot> customFields) {
        customFields.forEach(field ->
            rowMap.put(field.getLabel(), extraInfo.getOrDefault(field.getFieldName(), null))
        );
    }

    /**
     * 导出Excel
     */
    private void exportToExcel(HttpServletResponse response, String activityName,
                              List<Map<String, Object>> dataList) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = URLEncoder.encode("群众报名记录_" + activityName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            try (ServletOutputStream out = response.getOutputStream();
                 ExcelWriter writer = ExcelUtil.getWriter(true)) {

                // 计算标题单元格合并数量
                int columnCount = dataList.isEmpty() ? 10 : dataList.get(0).size() - 1;

                // 合并单元格作为标题
                writer.merge(columnCount, activityName + " - 群众报名记录");

                // 一次性写出内容
                writer.write(dataList, true);

                // 写出到流
                writer.flush(out, true);
            }
        } catch (IOException e) {
            throw new BusinessException(ResultCode.FAIL, "导出失败：" + e.getMessage());
        }
    }

    @Override
    public Integer getJoinMassesNum(DataScreenParams params) {
        return baseMapper.getJoinMassesNum(params);
    }

    @Override
    public IPage<ApplyPublicPageDto> pageMyApplyPublic(ZyzActivityApplyPublicParams params) {
        if (params == null) {
            params = new ZyzActivityApplyPublicParams();
        }
        // 获取当前用户ID
        Long userId = (Long) Oauth2Util.getUserId();
        params.setMyUserId(userId);
        String mobile = Oauth2Util.getMobile();
        params.setMyMobile(mobile);
        // 创建分页对象
        Page<ApplyPublicPageDto> page = Page.of(params.getCurrentPage(), params.getPageSize());
        // 直接使用mapper方法返回DTO对象
        return baseMapper.pageMyApplyPublic(page, params);
    }
}
