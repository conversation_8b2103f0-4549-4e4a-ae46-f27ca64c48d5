package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.AwardImportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerCertificate;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerCertificateParams;
import fykj.microservice.core.base.IBaseService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 志愿者证书关联表
 * <p>
 * 服务类
 *
 * <AUTHOR>
 * @since 2024-03-12
 */
public interface IZyzVolunteerCertificateService extends IBaseService<ZyzVolunteerCertificate> {

    /**
     * 证书分页获取
     *
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<ZyzVolunteerCertificate> getPages(ZyzVolunteerCertificateParams params);

    /**
     * 获取志愿者的证书列表
     *
     * @param volunteerId 志愿者ID
     * @return 证书列表
     */
    List<ZyzVolunteerCertificate> getAwardedCertificateList(Long volunteerId);

    /**
     * 获取志愿者的证书列表
     *
     * @param phone 志愿者手机号码
     * @return 证书列表
     */
    List<ZyzVolunteerCertificate> getAwardedCertificateList(String phone);

    /**
     * 志愿者数据导入
     *
     * @param excel 文件
     * @return 错误列表
     */
    List<AwardImportDto> dataImport(MultipartFile excel, Long awardCertificateId);

    /**
     * 判断证书是否已经颁发
     *
     * @param id 证书id
     * @return 是否已经使用
     */
    boolean checkCertificateUsed(long id);

    /**
     * 批量生成证书
     *
     * @param ids 证书获取表id列表
     * @return 是否重新生成成功
     */
    boolean generateCertificatePdfByIds(List<Long> ids);


    /**
     * 小程序调用生成证书并返回证书信息
     *
     * @param id 证书获取表id列表
     * @return 是否重新生成成功
     */
    ZyzVolunteerCertificate generateCertificatePdfById(Long id);

    /**
     * 证书效果预览
     * @param id 证书id
     * @return
     */

    Map<String, String> imagePreview(long id);
}

