package com.fykj.scaffold.zyz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.zyz.domain.dto.*;
import com.fykj.scaffold.zyz.domain.entity.ZyzProject;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ZyzProjectParams;
import fykj.microservice.core.base.IBaseService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 项目表
 * <p>
 * 服务类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
public interface IZyzProjectService extends IBaseService<ZyzProject> {

    /**
     * 保存项目
     *
     * @param entity
     */
    void saveProject(ZyzProject entity);

    /**
     * 根据id获取项目
     *
     * @param id
     * @return
     */
    ZyzProject getProjectById(Long id);

    /**
     * 根据id获取项目
     *
     * @param id
     * @return
     */
    ZyzProject getCockpitProjectById(Long id);

    /**
     * 获取项目--分页查询
     *
     * @param params
     * @return
     */
    IPage<ZyzProject> getPages(ZyzProjectParams params);


    /**
     * 获取项目
     *
     * @param params
     * @return
     */
    List<ProjectExportDto> getList(ZyzProjectParams params);

    /**
     * @param entity
     */
    void updateProject(ZyzProject entity);

    /**
     * 提交项目
     *
     * @param id
     * @return
     */
    boolean render(Long id);


    /**
     * 保存项目并提交
     *
     * @param entity
     * @return
     */
    boolean renderByProject(ZyzProject entity);

    /**
     * 项目审核
     *
     * @param idList
     * @param status
     * @param remark
     */
    void audit(List<Long> idList, Boolean status, String remark);

    /**
     * 项目导入月报
     *
     * @param entity
     */
    void importReport(ZyzProject entity);

    /**
     * 查询团队的项目id
     *
     * @param
     * @return
     */
    List<Long> listForTeamProjectIds();

    /**
     * 项目撤回
     *
     * @param id
     */
    void recall(Long id, Boolean isFinish);

    /**
     * 项目完成
     *
     * @param entity
     */
    void finishSave(ZyzProject entity);

    /**
     * 项目完成审核
     *
     * @param id
     * @param status
     * @param remark
     */
    void finishAudit(List<Long> idList, Boolean status, String remark);

    /**
     * 项目执行
     *
     * @param dto
     */
    void execute(ProjectExecuteDto dto);


    IPage<ZyzProject> getPagesForPc(ZyzProjectParams params);

    /**
     * 修改项目并提交
     *
     * @param entity
     * @return
     */
    boolean renderByUpdateProject(ZyzProject entity);

    /**
     * 获取项目申报待审核数量
     *
     * @return
     */
    Integer getProjectApplyWaitAuditNum();

    /**
     * 获取项目结项待审核数量
     *
     * @return
     */
    Integer getProjectSumWaitAuditNum();

    /**
     * 获取项目对接待审核数量
     *
     * @return 待审核数量
     */
    Integer getProjectDockingWaitAuditNum();

    /**
     * 统计数量
     *
     * @param params
     * @return
     */
    Integer sumNum(DataScreenParams params);

    /**
     * 获取项目物资累积金额
     *
     * @param params
     * @return
     */
    BigDecimal getMaterialTotalAmount(DataScreenParams params);

    /**
     * 项目终止
     *
     * @param idList 项目 ids
     * @param remark 备注
     */
    void stop(List<Long> idList, String remark);

    /**
     * 对接审核启动
     *
     * @param id 项目id
     */
    void dockingAuditStart(Long id);

    /**
     * 对接审核撤回
     *
     * @param id 项目 id
     */
    void dockingAuditRecall(Long id);

    /**
     * 项目对接审核
     *
     * @param id     项目 id
     * @param status 通过还是驳回
     * @param remark 备注
     */
    void dockingAudit(List<Long> idList, Boolean status, String remark);


    /**
     * 批量下载学习成果附件
     *
     * @param request  查询参数
     * @param response 查询参数
     * @param type     分类
     * @param ids      查询参数
     */
    void downloadResultFile(HttpServletRequest request, HttpServletResponse response, String ids, Integer type);

    /**
     * 获取项目报表信息
     *
     * @param params 查询参数
     * @return 项目列表
     */
    Map<String, List<ProjectSumReportDto>> getProjectSumReport(ZyzProjectParams params);

    /**
     * 获取项目报表导出信息
     *
     * @param params 查询参数
     * @return 项目列表
     */
    List<ProjectSumReportDto> getProjectSumReportExport(ZyzProjectParams params);

    /**
     * 公益伙伴项目导出
     *
     * @param params 查询参数
     * @return 列表
     */
    List<ProjExportDto> getListForExport(ZyzProjectParams params);


    /**
     * 修改前端展示状态
     *
     * @param idList
     * @param status
     */
    void displayStatus(List<Long> idList, Boolean status);

    /**
     * 获取项目书详情
     *
     * @param projectId 项目id
     * @return 项目书内容
     */
    ProjectFormDto getProjectFormData(Long projectId);

    /**
     * 获取团队项目列表
     * @return
     */

    List<ZyzProject> getTeamProjectList();


    /**
     * 前端展示年份列表
     * @return
     */
    List<String> getYearList();


    /**
     * 获取最大年份
     *
     * @return
     */
    String getMaxYear();

    /**
     * 驾驶舱-公益伙伴计划看板项目列表
     * @param year
     * @return
     */
    List<ProjectCockpitListDto> cockpitList(String year);

    /**
     * 驾驶舱-公益伙伴计划看板项目概览
     * @param year
     * @return
     */
    ProjectCockpitDto cockpitOverview(String year);

}

