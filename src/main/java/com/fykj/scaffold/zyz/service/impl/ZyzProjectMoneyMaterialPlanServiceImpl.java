package com.fykj.scaffold.zyz.service.impl;

import com.fykj.scaffold.zyz.domain.entity.ZyzProjectMoneyMaterialPlan;
import com.fykj.scaffold.zyz.mapper.ZyzProjectMoneyMaterialPlanMapper;
import com.fykj.scaffold.zyz.service.IZyzProjectMoneyMaterialPlanService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 资金/物资使用计划表
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzProjectMoneyMaterialPlanServiceImpl extends BaseServiceImpl<ZyzProjectMoneyMaterialPlanMapper, ZyzProjectMoneyMaterialPlan> implements IZyzProjectMoneyMaterialPlanService {


    @Override
    public void saveProjectMoneyMaterialPlan(List<ZyzProjectMoneyMaterialPlan> projectMoneyMaterialPlanList, Long projectId) {
        baseMapper.deleteList(projectId);
        projectMoneyMaterialPlanList.forEach(item->{
            item.setProjectId(projectId);
            save(item);
        });
    }
}