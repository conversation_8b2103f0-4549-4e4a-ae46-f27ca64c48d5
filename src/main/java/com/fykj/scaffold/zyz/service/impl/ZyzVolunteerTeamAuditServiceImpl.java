package com.fykj.scaffold.zyz.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.mq.cons.TopicCons;
import com.fykj.scaffold.mq.event.SgbTeamMemberSyncEvent;
import com.fykj.scaffold.mq.event.TeamMemberSyncEvent;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.msg_send.ISendTmpMsg;
import com.fykj.scaffold.zyz.conns.ZyzCons;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeam;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeamAudit;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamAuditParams;
import com.fykj.scaffold.zyz.mapper.ZyzVolunteerTeamAuditMapper;
import com.fykj.scaffold.zyz.service.IZyzTeamService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamAuditService;
import com.fykj.scaffold.zyz.service.IZyzVolunteerTeamService;
import exception.BusinessException;
import fykj.microservice.core.base.BaseServiceImpl;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import result.ResultCode;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.fykj.scaffold.support.conns.SysMsgTmpCons.SMT_TEAM_JOIN_AUDIT;
import static constants.Mark.COMMA;


/**
 * 志愿者与团队关系审核表
 * <p>
 * 服务实现类
 *
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzVolunteerTeamAuditServiceImpl extends BaseServiceImpl<ZyzVolunteerTeamAuditMapper, ZyzVolunteerTeamAudit> implements IZyzVolunteerTeamAuditService {
    @Autowired
    private IZyzVolunteerTeamService volunteerTeamService;

    @Autowired
    private RocketMQTemplate mqTemplate;

    @Autowired
    private IZyzTeamService teamService;

    @Autowired
    private ISendTmpMsg sendTmpMsg;


    @Override
    public IPage<VolunteerTeamAuditDto> getPages(ZyzVolunteerTeamAuditParams params) {
        return baseMapper.getPages(params.getPage(), params);
    }

    @Override
    public Integer getJoinTeamWaitAuditNum() {
        return baseMapper.getJoinTeamWaitAuditNum(ZyzCons.VOLUNTEER_AUDIT_STATUS_CHECK);
    }

    @Override
    public boolean volunteerAudit(String ids, String remake, String auditStatus) {
        List<Long> idList = Arrays.stream(ids.split(COMMA)).map(Long::valueOf).collect(Collectors.toList());
        List<Long> volunteerIds = new ArrayList<>();
        List<ZyzVolunteerTeamAudit> res = new ArrayList<>();
        idList.forEach(item -> {
            ZyzVolunteerTeamAudit zyzVolunteerTeamAudit = getById(item);
            volunteerIds.add(zyzVolunteerTeamAudit.getVolunteerId());
            //审核通过，中间表添加记录，根据id修改审核表状态为通过
            if (ZyzCons.VOLUNTEER_AUDIT_STATUS_PASS.equals(auditStatus)) {
                zyzVolunteerTeamAudit.setStatus(ZyzCons.VOLUNTEER_AUDIT_STATUS_PASS);
                ZyzVolunteerTeam zyzVolunteerTeam = new ZyzVolunteerTeam();
                zyzVolunteerTeam.setTeamId(zyzVolunteerTeamAudit.getTeamId());
                zyzVolunteerTeam.setVolunteerId(zyzVolunteerTeamAudit.getVolunteerId());
                zyzVolunteerTeam.setDutyType(Cons.DUTY_TYPE_COMMON);
                zyzVolunteerTeam.setJoinTime(LocalDateTime.now());
                zyzVolunteerTeam.setIsSync(Cons.PlatformSyncState.WAIT_SYNC);
                //查询团队中是否已存在此人
                if (volunteerTeamService.getByVolunteerIdAndTeamId(zyzVolunteerTeam.getVolunteerId(), zyzVolunteerTeam.getTeamId()) != null) {
                    throw new BusinessException(ResultCode.FAIL, "该志愿者已审核，请勿重新操作");
                }
                volunteerTeamService.save(zyzVolunteerTeam);
                teamService.updateTeamMemberNum(zyzVolunteerTeam.getTeamId());
            }
            //审核不通过，根据id修改状态为不通过
            if (ZyzCons.VOLUNTEER_AUDIT_STATUS_NOT_PASS.equals(auditStatus)) {
                zyzVolunteerTeamAudit.setStatus(ZyzCons.VOLUNTEER_AUDIT_STATUS_NOT_PASS);
                zyzVolunteerTeamAudit.setRemark(remake);
            }
            res.add(zyzVolunteerTeamAudit);
            updateById(zyzVolunteerTeamAudit);

            //发送信息给志愿者
            sendTmpMsg.sendToVolunteer(SMT_TEAM_JOIN_AUDIT,
                    volunteerIds,
                    ZyzCons.VOLUNTEER_AUDIT_STATUS_PASS.equals(auditStatus) ? "通过" : "不通过",
                    teamService.getById(zyzVolunteerTeamAudit.getTeamId()).getName());
        });
        //审核通过发mq通知同步 - 双重推送
        if (ZyzCons.VOLUNTEER_AUDIT_STATUS_PASS.equals(auditStatus)) {
            // 原有同步
            mqTemplate.syncSend(TopicCons.TEAM_MEMBER_SYNC,
                    res
                            .stream()
                            .map(it -> MessageBuilder
                                    .withPayload(TeamMemberSyncEvent
                                            .builder()
                                            .volunteerId(it.getVolunteerId())
                                            .teamId(it.getTeamId())
                                            .build())
                                    .build()).collect(Collectors.toList()));

            // SGB同步
            mqTemplate.syncSend(TopicCons.SGB_TEAM_MEMBER_SYNC,
                    res
                            .stream()
                            .map(it -> MessageBuilder
                                    .withPayload(SgbTeamMemberSyncEvent
                                            .builder()
                                            .volunteerId(it.getVolunteerId())
                                            .teamId(it.getTeamId())
                                            .build())
                                    .build()).collect(Collectors.toList()));
        }
        return true;
    }
}
