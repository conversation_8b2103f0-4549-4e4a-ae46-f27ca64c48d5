package com.fykj.scaffold.zyz.service.impl;


import com.fykj.scaffold.zyz.domain.entity.SysConvertOrgName;
import com.fykj.scaffold.zyz.mapper.SysConvertOrgNameMapper;
import com.fykj.scaffold.zyz.service.ISysConvertOrgNameService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 
 *
 * 服务实现类
 * <AUTHOR> @email ${email}
 * @date 2023-03-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class SysConvertOrgNameServiceImpl extends BaseServiceImpl<SysConvertOrgNameMapper, SysConvertOrgName> implements ISysConvertOrgNameService {


}