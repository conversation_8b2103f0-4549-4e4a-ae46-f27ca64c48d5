package com.fykj.scaffold.zyz.service.impl;

import com.fykj.scaffold.security.business.domain.entity.SysOrg;
import com.fykj.scaffold.security.business.service.ISysOrgService;
import com.fykj.scaffold.support.conns.Cons;
import com.fykj.scaffold.support.utils.Oauth2Util;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShow;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityTimePeriodShowLog;
import com.fykj.scaffold.zyz.mapper.ZyzActivityTimePeriodShowLogMapper;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowLogService;
import com.fykj.scaffold.zyz.service.IZyzActivityTimePeriodShowService;
import fykj.microservice.core.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import utils.StringUtil;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 活动公示操作记录表
 * <p>
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-19
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ZyzActivityTimePeriodShowLogServiceImpl extends BaseServiceImpl<ZyzActivityTimePeriodShowLogMapper, ZyzActivityTimePeriodShowLog> implements IZyzActivityTimePeriodShowLogService {

    @Autowired
    private IZyzActivityTimePeriodShowService showService;

    @Autowired
    private ISysOrgService orgService;

    @Override
    public boolean saveLog(Long activityTimePeriodShowId, Long activityTimePeriodId, Long activityId, String operateType, String remark) {
        ZyzActivityTimePeriodShowLog log = new ZyzActivityTimePeriodShowLog();
        //关联的 id 信息
        log.setActivityId(activityId);
        log.setActivityTimePeriodId(activityTimePeriodId);
        log.setActivityTimePeriodShowId(activityTimePeriodShowId);
        //获取当前人登录信息
        log.setOperateTime(LocalDateTime.now());
        log.setOperatorName(Oauth2Util.getName());
        log.setOperator((Long) Oauth2Util.getUserId());
        log.setOperateType(operateType);
        log.setRemark(remark);
        //判断资源是否为团队创建，若是，则给orgCode赋值为需求发布组织code
        String orgCode;
        if (Cons.RoleCode.ROLE_CODE_TEAM_ADMIN.equals(Oauth2Util.getManagerCapacity())) {
            ZyzActivityTimePeriodShow show = showService.getById(activityTimePeriodShowId);
            orgCode = show.getPublishOrgCode();
        } else {
            //若不是，则给orgCode赋值为当前登录人code
            orgCode = Oauth2Util.getOrgCode();
        }
        log.setOperatorOrgCode(orgCode);
        SysOrg org = StringUtil.isEmpty(orgCode) ? null : orgService.getByCode(orgCode);
        log.setOperatorOrgName(org == null ? null : org.getName());
        return super.save(log);
    }

    @Override
    public List<ZyzActivityTimePeriodShowLog> getLogsByShowId(Long showId) {
        return lambdaQuery().eq(ZyzActivityTimePeriodShowLog::getActivityTimePeriodShowId, showId).orderByDesc(ZyzActivityTimePeriodShowLog::getOperateTime).list();
    }

}
