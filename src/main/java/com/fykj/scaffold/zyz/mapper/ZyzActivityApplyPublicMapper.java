package com.fykj.scaffold.zyz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fykj.scaffold.zyz.domain.dto.ApplyPublicPageDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityApplyPublic;
import com.fykj.scaffold.zyz.domain.params.DataScreenParams;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityApplyPublicParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 活动群众报名表
 *
 * Mapper 接口
 * <AUTHOR>
 * @email ${email}
 * @date 2023-07-31
 */
public interface ZyzActivityApplyPublicMapper extends BaseMapper<ZyzActivityApplyPublic> {

    /**
     * 分页查询服务时长
     *
     * @param page
     * @param params
     * @return
     */
//    @DataFilter(deptCodeLinkColumn = "sp.code_prefix")
    IPage<ZyzActivityApplyPublic> pageOrListForApplyPublic(Page<ZyzActivityApplyPublic> page, @Param("params") ZyzActivityApplyPublicParams params);

    /**
     * 分页查询群众报名
     * @param params
     */
    List<ZyzActivityApplyPublic> pageOrListForApplyPublic(@Param("params")  ZyzActivityApplyPublicParams params);

    /**
     * 获取群众参与人次
     * @return
     */
    Integer getJoinMassesNum(@Param("params") DataScreenParams params);
    
    /**
     * 我的群众报名分页查询
     * @param page 分页信息
     * @param params 查询参数
     * @return 分页数据
     */
    IPage<ApplyPublicPageDto> pageMyApplyPublic(Page<ApplyPublicPageDto> page, @Param("params") ZyzActivityApplyPublicParams params);
}
