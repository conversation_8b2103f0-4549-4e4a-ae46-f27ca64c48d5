package com.fykj.scaffold.zyz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportAllDto;
import com.fykj.scaffold.zyz.domain.dto.ZyzActivityScheduleDetailExportDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzActivityScheduleDetail;
import com.fykj.scaffold.zyz.domain.params.ZyzActivityScheduleParams;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 阵地计划详情
 * <p>
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
public interface ZyzActivityScheduleDetailMapper extends BaseMapper<ZyzActivityScheduleDetail> {


    /**
     * 获取阵地计划详情
     *
     * @param scheduleId 计划id
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetail> listForScheduleDetail(@Param("scheduleId") Long scheduleId);

    /**
     * 获取阵地计划详情(用户端
     *
     * @param scheduleId 计划id
     * @return 详情列表
     */
    List<ZyzActivityScheduleDetail> listForScheduleDetailFrontend(@Param("scheduleId") Long scheduleId);

    /**
     * 阵地计划明细导出
     *
     * @param params cha
     * @return 明细列表
     */
    @DataFilter(deptCodeLinkColumn = "o.code_prefix")
    List<ZyzActivityScheduleDetailExportDto> listForScheduleDetailExport(@Param("params") ZyzActivityScheduleParams params);

    /**
     * 阵地计划明细导出全部字段
     *
     * @param params 查询参数
     * @return 明细列表
     */
    @DataFilter(deptCodeLinkColumn = "o.code_prefix")
    List<ZyzActivityScheduleDetailExportAllDto> listForScheduleDetailExportAll(@Param("params") ZyzActivityScheduleParams params);
}
