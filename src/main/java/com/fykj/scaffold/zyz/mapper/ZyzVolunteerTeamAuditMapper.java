package com.fykj.scaffold.zyz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fykj.scaffold.config.mybatisplus.DataFilter;
import com.fykj.scaffold.zyz.domain.dto.VolunteerTeamAuditDto;
import com.fykj.scaffold.zyz.domain.entity.ZyzVolunteerTeamAudit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fykj.scaffold.zyz.domain.params.ZyzVolunteerTeamAuditParams;
import org.apache.ibatis.annotations.Param;

/**
 * 志愿者与团队关系审核表
 *
 * Mapper 接口
 * <AUTHOR> @email ${email}
 * @date 2023-02-14
 */
public interface ZyzVolunteerTeamAuditMapper extends BaseMapper<ZyzVolunteerTeamAudit> {


    @DataFilter
    IPage<VolunteerTeamAuditDto> getPages(IPage<ZyzVolunteerTeamAudit> page, @Param("params") ZyzVolunteerTeamAuditParams params);

    /**
     * 根据状态获取加入团队数量
     * @param status
     * @return
     */
    @DataFilter(deptCodeLinkColumn = "o.code_prefix", teamId = "a.team_id")
    Integer getJoinTeamWaitAuditNum(@Param("status") String status);
}
