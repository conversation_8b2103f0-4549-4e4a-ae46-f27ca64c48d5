package com.fykj.scaffold.zyz.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HomePageVolunteerServiceDto {

    @ApiModelProperty("总注册人数")
    private int volunteerNum;

    @ApiModelProperty("志愿者服务总时长")
    private BigDecimal volunteerServiceTime;

    @ApiModelProperty("总团队数")
    private int teamNum;

    @ApiModelProperty("活跃志愿者总人数")
    private int activeVolunteerNum;

    @ApiModelProperty("总活动次数")
    private int activityNum;
}
