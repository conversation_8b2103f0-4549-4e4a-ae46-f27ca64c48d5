package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fykj.scaffold.support.prop_compare.PropCompare;
import com.fykj.scaffold.zyz.domain.dto.AttachmentDto;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 志愿者社区同名团队表
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "zyz_org_team", autoResultMap = true)
public class ZyzOrgTeam extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 团队名称
     */
    @TableField("name")
    @ApiModelProperty(value = "团队名称")
    private String name;
    /**
     * 是否民政注册团队
     */
    @TableField("civil_registration")
    @ApiModelProperty(value = "是否民政注册团队")
    private Boolean civilRegistration;
    /**
     * 业务主管单位
     */
    @TableField("business_supervisory_unit")
    @ApiModelProperty(value = "业务主管单位")
    private String businessSupervisoryUnit;
    /**
     * 注册地点/所在地
     */
    @TableField("register_place")
    @ApiModelProperty(value = "注册地点/所在地")
    private String registerPlace;
    /**
     * 所属领域
     */
    @TableField("belong_fields")
    @ApiModelProperty(value = "所属领域")
    private String belongFields;
    /**
     * 状态（启用，禁用）
     */
    @TableField("status")
    @ApiModelProperty(value = "状态（启用，禁用）")
    private Boolean status;
    /**
     * 组织架构code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "组织架构code")
    private String orgCode;
    /**
     * 团队编号
     */
    @TableField("team_no")
    @ApiModelProperty(value = "团队编号")
    private String teamNo;
    /**
     * 团队照片
     */
    @TableField("team_photo")
    @ApiModelProperty(value = "团队照片")
    private String teamPhoto;
    /**
     * 团队成员人数
     */
    @TableField("team_number")
    @ApiModelProperty(value = "团队成员人数")
    private Integer teamNumber;
    /**
     * 预计团队人数
     */
    @TableField("expect_team_num")
    @ApiModelProperty(value = "预计团队人数")
    private Integer expectTeamNum;
    /**
     * 团队审核状态(待审核，已审核，驳回)
     */
    @TableField("team_status")
    @ApiModelProperty(value = "团队审核状态(待审核，已审核，驳回)")
    private String teamStatus;
    /**
     * 审核组织名
     */
    @TableField("audit_org_code")
    @ApiModelProperty(value = "审核组织名")
    private String auditOrgCode;
    /**
     * 负责人姓名
     */
    @TableField("curator_name")
    @ApiModelProperty(value = "负责人姓名")
    private String curatorName;
    /**
     * 负责人证件号
     */
    @TableField("curator_card")
    @ApiModelProperty(value = "负责人证件号")
    private String curatorCard;
    /**
     * 负责人联系方式
     */
    @TableField("curator_contact")
    @ApiModelProperty(value = "负责人联系方式")
    private String curatorContact;
    /**
     * 负责人职位
     */
    @TableField("curator_job")
    @ApiModelProperty(value = "负责人职位")
    private String curatorJob;
    /**
     * 负责人是否是党员
     */
    @TableField("curator_party_member")
    @ApiModelProperty(value = "负责人是否是党员")
    private Boolean curatorPartyMember;
    /**
     * 负责人单位
     */
    @TableField("curator_department")
    @ApiModelProperty(value = "负责人单位")
    private String curatorDepartment;

    /**
     * 管理员姓名
     */
    @TableField("admin_name")
    @ApiModelProperty(value = "管理员姓名")
    private String adminName;
    /**
     * 管理员证件号
     */
    @TableField("admin_card")
    @ApiModelProperty(value = "管理员证件号")
    private String adminCard;
    /**
     * 管理员联系方式
     */
    @TableField("admin_contact")
    @ApiModelProperty(value = "管理员联系方式")
    private String adminContact;
    /**
     * 管理员邮箱
     */
    @TableField("admin_email")
    @ApiModelProperty(value = "管理员邮箱")
    private String adminEmail;
    /**
     * 管理员是否是党员
     */
    @TableField("admin_party_member")
    @ApiModelProperty(value = "管理员是否是党员")
    private Boolean adminPartyMember;
    /**
     * 是否有注册号
     */
    @TableField("has_regist")
    @ApiModelProperty(value = "是否有注册号")
    private Boolean hasRegist;
    /**
     * 管理员单位
     */
    @TableField("admin_department")
    @ApiModelProperty(value = "管理员单位")
    private String adminDepartment;
    /**
     * 管理员职位
     */
    @TableField("admin_job")
    @ApiModelProperty(value = "管理员职位")
    private String adminJob;
    /**
     * 注册号
     */
    @TableField("register_card")
    @ApiModelProperty(value = "注册号")
    private String registerCard;
    /**
     * 注册时间
     */
    @TableField("register_time")
    @ApiModelProperty(value = "注册时间")
    private LocalDateTime registerTime;
    /**
     * 有无专职财务人员
     */
    @TableField("has_financial")
    @ApiModelProperty(value = "有无专职财务人员")
    private Boolean hasFinancial;
    /**
     * 办公地点
     */
    @TableField("work_address")
    @ApiModelProperty(value = "办公地点")
    private String workAddress;
    /**
     * 成立时间
     */
    @TableField("founded")
    @ApiModelProperty(value = "成立时间")
    private LocalDate founded;
    /**
     * 团队介绍
     */
    @TableField("introduction")
    @ApiModelProperty(value = "团队介绍")
    private String introduction;
    /**
     * 服务内容
     */
    @TableField("services")
    @ApiModelProperty(value = "服务内容")
    private String services;
    /**
     * 报名条件
     */
    @TableField("join_requires")
    @ApiModelProperty(value = "报名条件")
    private String joinRequires;
    /**
     * 是否为重点团队
     */
    @TableField("is_emphasis")
    @ApiModelProperty(value = "是否为重点团队")
    private String isEmphasis;
    /**
     * 同步状态
     */
    @TableField("is_sync")
    @ApiModelProperty(value = "同步状态")
    private String isSync;
    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;
    /**
     * 同步成功后市区返回的Id
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "同步成功后市区返回的Id")
    private String syncId;
    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;
    /**
     * 是否活跃
     */
    @TableField("is_lively")
    @ApiModelProperty(value = "是否活跃")
    private Boolean lively;
    /**
     * 附件list
     */
    @TableField(value = "attachment_list", typeHandler = AttachmentDto.AttachmentListTypeHandler.class)
    @ApiModelProperty(value = "附件list")
    @PropCompare(name = "附件")
    private List<AttachmentDto> attachmentList = new ArrayList<>();
    /**
     * 审核类型（0：团队注册审核；1：团队信息变更审核）
     */
    @TableField("audit_type")
    @ApiModelProperty(value = "审核类型（0：团队注册审核；1：团队信息变更审核）")
    private Integer auditType;
    /**
     * 信息变更后审核状态
     */
    @TableField("info_change_audit_status")
    @ApiModelProperty(value = "信息变更后审核状态")
    private String infoChangeAuditStatus;
    /**
     * 同名社区的code
     */
    @TableField("related_org_code")
    @ApiModelProperty(value = "同名社区的code ")
    private String relatedOrgCode;

    /**
     * 同名社区的region code
     */
    @TableField("related_region_code")
    @ApiModelProperty(value = "同名社区的region code ")
    private String relatedRegionCode;

    /**
     * 职能部门市区同步后ID
     */
    @TableField("dep_sync_id")
    @ApiModelProperty(value = "职能部门市区同步后ID")
    private String depSyncId;

}
