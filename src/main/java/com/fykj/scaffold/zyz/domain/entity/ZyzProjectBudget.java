package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 项目预算明细
 *
 * <AUTHOR> @email ${email}
 * @date 2023-05-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_project_budget")
public class ZyzProjectBudget extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 项目预算名称
     */
    @TableField("name")
    @ApiModelProperty(value = "项目预算名称")
    private String name;

    /**
     * 金额（元）
     */
    @TableField("amount")
    @ApiModelProperty(value = "金额（元）")
    private Integer amount;

    /**
     * 说明
     */
    @TableField("description")
    @ApiModelProperty(value = "说明")
    private String description;

    /**
     * 项目id
     */
    @TableField("project_id")
    @ApiModelProperty(value = "项目id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long projectId;


}
