package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 阵地计划（小计划）关联活动计划（大活动）
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_activity_schedule_act_plan")
public class ZyzActivityScheduleActPlan extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 计划id
     */
    @TableField("schedule_id")
    @ApiModelProperty(value = "计划id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleId;
    /**
     * 计划详情id
     */
    @TableField("schedule_detail_id")
    @ApiModelProperty(value = "计划详情id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long scheduleDetailId;
    /**
     * 活动预告编号(计划详情)（市区返回）
     */
    @TableField(exist=false)
    @ApiModelProperty(value = "活动预告编号(计划详情)（市区返回）")
    private String preId;
    /**
     * 活动id（大活动id）
     */
    @TableField("activity_id")
    @ApiModelProperty(value = "活动id（大活动id）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;
    /**
     * 活动登记编号（大活动）（市区返回）
     */
    @TableField(exist=false)
    @ApiModelProperty(value = "活动登记编号（大活动）（市区返回）")
    private String actpId;
    /**
     * 同步状态
     */
    @TableField("sync")
    @ApiModelProperty(value = "同步状态")
    private String sync;
    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    private LocalDateTime syncTime;
    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

}
