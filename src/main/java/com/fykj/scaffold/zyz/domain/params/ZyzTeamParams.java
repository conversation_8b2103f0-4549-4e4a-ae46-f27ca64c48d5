package com.fykj.scaffold.zyz.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 志愿者团队表
 * 查询参数
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "团队表查询参数")
public class ZyzTeamParams extends BaseParams {

    @MatchType(value = QueryType.EQ, fieldName = "volunteer_id")
    private Long volunteerId;
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String teamName;
    @MatchType(value = QueryType.EQ, fieldName = "org_code")
    private String orgCode;
    @MatchType(value = QueryType.LIKE, fieldName = "name")
    private String name;
    @MatchType(value = QueryType.EQ)
    private String admin;
    @MatchType(value = QueryType.LIKE)
    private String curator;
    @MatchType(value = QueryType.EQ, fieldName = "team_no")
    private String teamNo;
    @MatchType(value = QueryType.EQ, fieldName = "status")
    private Boolean status;
    @MatchType(value = QueryType.EQ, fieldName = "is_emphasis")
    private String isEmphasis;
    @MatchType(value = QueryType.EQ, fieldName = "team_status")
    private String teamStatus;
    @ApiModelProperty(value="所属领域id")
    private String fieldId;
    @ApiModelProperty(value="排序 true为人数正序,false为人数倒序,空为成立时间排序")
    private Boolean orderByNum;
    @ApiModelProperty(value="是否按照活跃度排序，要排序，传 true，不排序传 null 或 false")
    private Boolean orderByActive;
    private String startCreateDate;

    /**
     * 审核查询参数-本组织code
     */
    private String myOrgCode;

    /**
     * 需要我审核的
     */
    private Boolean needAudit;

    /**
     * 审核类型
     */
    private Integer auditType;

    private String endCreateDate;

    @ApiModelProperty(value = "管理员姓名")
    @MatchType(value = QueryType.LIKE)
    private String adminName;

    @ApiModelProperty(value = "管理员身份证号码")
    @MatchType(value = QueryType.LIKE)
    private String adminCard;

    @ApiModelProperty(value = "管理员联系方式")
    @MatchType(value = QueryType.LIKE)
    private String adminContact;

    public ZyzTeamParams(BaseParams baseParams) {
        setCurrentPage(baseParams.getCurrentPage());
        setPageSize(baseParams.getPageSize());
        setOrders(baseParams.getOrders());
    }

}
