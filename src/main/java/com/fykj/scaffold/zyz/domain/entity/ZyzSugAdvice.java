package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.support.utils.Desensitise;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 咨询建议-实体类
 *
 * <AUTHOR>
 * @date 2024-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "zyz_sug_advice", autoResultMap = true)
public class ZyzSugAdvice extends BaseEntity {

    private static final long serialVersionUID = 8416536794756760919L;


    /**
     * 用户id
     */
    @TableField("user_id")
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 类型
     */
	@DictTrans(transTo = "typeText")
    @TableField("type")
    @ApiModelProperty(value = "类型")
    private String type;


	@TableField(exist = false)
	@ApiModelProperty(value = "类型文本")
	private String typeText;

    /**
     * 姓名
     */
    @TableField("name")
    @ApiModelProperty(value = "姓名")
    private String name;

    /**
     * 电话
     */
    @Desensitise()
    @TableField("mobile")
    @ApiModelProperty(value = "电话")
    private String mobile;

    /**
     * 咨询内容
     */
    @TableField("advice_content")
    @ApiModelProperty(value = "咨询内容")
    private String adviceContent;

    /**
     * 状态1-已回复，0-未回复
     */
    @TableField("status")
    @ApiModelProperty(value = "状态1-已回复，0-待回复")
    private Boolean status;
    /**
     * 手机端展示
     */
    @TableField("is_front_show")
    @ApiModelProperty(value = "手机端展示")
    private Boolean frontShow;

    /**
     * 回复内容
     */
    @TableField("reply_content")
    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    /**
     * 回复时间
     */
    @TableField("reply_date")
    @ApiModelProperty(value = "回复时间")
    @JsonFormat(pattern = DATETIME_FORMAT)
    @DateTimeFormat(pattern = DATETIME_FORMAT)
    private LocalDateTime replyDate;

    /**
     * 图片附件
     */
    @TableField("image_url")
    @ApiModelProperty(value = "图片附件")
    private String imageUrl;

}
