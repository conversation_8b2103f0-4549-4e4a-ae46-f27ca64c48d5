package com.fykj.scaffold.zyz.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 勋章批量授予导入模型
 *
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("勋章批量授予导入模型")
public class BadgeImportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 志愿者姓名
     */
    @ExcelProperty(value = "*志愿者姓名")
    private String name;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "身份证号码")
    private String certificateId;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "*手机号码")
    private String phone;

    /**
     * 导入失败原因
     */
    @ExcelProperty(value = "导入失败原因")
    private String failReason;
} 