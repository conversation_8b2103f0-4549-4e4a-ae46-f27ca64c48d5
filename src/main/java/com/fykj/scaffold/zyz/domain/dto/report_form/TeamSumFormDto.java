package com.fykj.scaffold.zyz.domain.dto.report_form;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 活动报表dto-团队统计
 */
@Data
@NoArgsConstructor
public class TeamSumFormDto {

    /**
     * 团队名称
     */
    @ExcelIgnore
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 团队名称
     */
    @ExcelProperty("团队名称")
    @ColumnWidth(40)
    private String teamName;

    /**
     * 实际人数
     */
    @ExcelProperty("实际人数")
    @ColumnWidth(30)
    private int teamVolunteerNum;

    /**
     * 上级组织
     */
    @ExcelProperty("上级组织")
    @ColumnWidth(30)
    private String teamOrgName;

    /**
     * 当年活跃人数
     */
    @ExcelProperty("当年活跃人数")
    @ColumnWidth(30)
    private int teamThisYearLivelyVolunteerNum;

    /**
     * 当年服务时长
     */
    @ExcelProperty("当年服务时长")
    @ColumnWidth(30)
    private BigDecimal teamThisYearServiceDurationTotal;

    /**
     * 当年活动次数
     */
    @ExcelProperty("当年活动次数")
    @ColumnWidth(30)
    private int teamThisYearActNum;
}
