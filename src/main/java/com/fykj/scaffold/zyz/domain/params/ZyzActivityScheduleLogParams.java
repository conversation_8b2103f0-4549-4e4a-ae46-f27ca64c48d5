package com.fykj.scaffold.zyz.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 阵地计划操作记录表
 * 查询参数
 *
 * <AUTHOR>
 * @date 2024-04-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "阵地计划操作记录表查询参数")
public class ZyzActivityScheduleLogParams extends BaseParams {

}
