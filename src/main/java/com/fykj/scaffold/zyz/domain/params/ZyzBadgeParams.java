package com.fykj.scaffold.zyz.domain.params;

import fykj.microservice.core.base.BaseParams;
import fykj.microservice.core.support.wrapper.annotation.MatchType;
import fykj.microservice.core.support.wrapper.enums.QueryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 勋章信息-查询参数
 *
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel("勋章信息-查询参数")
public class ZyzBadgeParams extends BaseParams {

    private static final long serialVersionUID = 5409877578262332752L;

    @ApiModelProperty(value = "勋章名称")
    @MatchType(value = QueryType.LIKE)
    private String name;

    @ApiModelProperty(value = "状态")
    @MatchType(value = QueryType.EQ)
    private Boolean status;

    @ApiModelProperty(value = "是否置顶")
    @MatchType(value = QueryType.EQ)
    private Boolean top;

}
