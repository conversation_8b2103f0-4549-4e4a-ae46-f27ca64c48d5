package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class ServiceLongForBlockChainParamsDto {
    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = " 签到开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)  //格式化前台日期参数注解
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    @ApiModelProperty(value = " 签到结束时间")
    private LocalDateTime endTime;
}
