package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadge;
import com.fykj.scaffold.zyz.domain.entity.ZyzBadgeTradeListing;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 勋章交易详情DTO
 *
 * @date 2025-05-09
 */
@Data
@ApiModel("勋章交易详情数据模型")
public class ZyzBadgeTradeDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 勋章基本信息
     */
    @ApiModelProperty(value = "勋章基本信息")
    private ZyzBadge badgeInfo;

    /**
     * 交易上架信息
     */
    @ApiModelProperty(value = "交易上架信息")
    private ZyzBadgeTradeListing listingInfo;

    /**
     * 发布者ID
     */
    @ApiModelProperty(value = "发布者ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long publisherId;

    /**
     * 上架信息ID
     */
    @ApiModelProperty(value = "上架信息ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long listingId;
    
    /**
     * 发布者昵称
     */
    @ApiModelProperty(value = "发布者昵称")
    private String publisherName;
    
    /**
     * 是否是当前用户发布的
     */
    @ApiModelProperty(value = "是否是当前用户发布的")
    private Boolean currentUserPublished;

    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    private LocalDate publishDate;
}