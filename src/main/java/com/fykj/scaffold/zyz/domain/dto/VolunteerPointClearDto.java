package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class VolunteerPointClearDto {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private BigDecimal totalPoint;

    private BigDecimal thisYearAchievePoint;
}
