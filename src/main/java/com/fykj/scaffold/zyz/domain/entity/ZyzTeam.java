package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.prop_compare.PropChangeInfo;
import com.fykj.scaffold.support.prop_compare.PropCompare;
import com.fykj.scaffold.support.utils.Desensitise;
import com.fykj.scaffold.zyz.domain.dto.AttachmentDto;
import fykj.microservice.cache.support.DictTrans;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

;

/**
 * 志愿者团队表
 *
 * <AUTHOR> @email ${email}
 * @date 2023-01-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName(value = "zyz_team", autoResultMap = true)
public class ZyzTeam extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 团队名称
     */
    @TableField("name")
    @ApiModelProperty(value = "团队名称")
    @PropCompare(name = "团队名称")
    private String name;

    /**
     * 团队预计成员人数
     */
    @TableField("expect_team_num")
    @ApiModelProperty(value = "团队预计成员人数")
    @PropCompare(name = "团队预计成员人数")
    private Integer expectTeamNum;

    /**
     * 是否民政注册团队
     */
    @TableField("civil_registration")
    @ApiModelProperty(value = "是否民政注册团队")
    @PropCompare(name = "团队性质")
    private Boolean civilRegistration;

    /**
     * 成立时间/注册时间
     */
    @TableField("founded")
    @ApiModelProperty(value = "成立时间/注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @PropCompare(name = "成立时间/注册时间")
    private LocalDate founded;

    /**
     * 业务主管单位
     */
    @TableField("business_supervisory_unit")
    @ApiModelProperty(value = "业务主管单位")
    @PropCompare(name = "业务主管单位")
    private String businessSupervisoryUnit;

    /**
     * 注册地点/所在地(街道/社区)
     */
    @TableField("register_place")
    @ApiModelProperty(value = "注册地点/所在地(街道/社区)")
    @PropCompare(name = "注册地点/所在地(街道/社区)")
    private String registerPlace;

    /**
     * 附件list
     */
    @TableField(value = "attachment_list", typeHandler = AttachmentDto.AttachmentListTypeHandler.class)
    @ApiModelProperty(value = "附件list")
    @PropCompare(name = "附件")
    private List<AttachmentDto> attachmentList = new ArrayList<>();

    /**
     * 上级组织code
     */
    @TableField("org_code")
    @ApiModelProperty(value = "上级组织code")
    private String orgCode;

    /**
     * 组织架构编码link list
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "组织架构编码link list")
    private List<String> orgCodeList;

    /**
     * 上级组织名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "上级组织名称")
    private String orgName;

    /**
     * 是否有注册号
     */
    @TableField("has_regist")
    @ApiModelProperty(value = "是否有注册号")
    @PropCompare(name = "是否有注册号")
    private Boolean hasRegist;

    /**
     * 团队注册号
     */
    @TableField("register_card")
    @ApiModelProperty(value = "团队注册号")
    @PropCompare(name = "团队注册号")
    private String registerCard;

    /**
     * 所属领域
     */
    @TableField("belong_fields")
    @ApiModelProperty(value = "所属领域")
    @PropCompare(name = "所属领域")
    private String belongFields;

    /**
     * 所属领域list
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "所属领域list")
    private List<String> belongFieldList;

    /**
     * 所属领域Text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "所属领域Text")
    private String belongFieldsText;

    /**
     * 办公地点
     */
    @TableField("work_address")
    @ApiModelProperty(value = "办公地点")
    @PropCompare(name = "办公地点")
    private String workAddress;

    /**
     * 团队介绍
     */
    @TableField("introduction")
    @ApiModelProperty(value = "团队介绍")
    @PropCompare(name = "团队介绍")
    private String introduction;

    /**
     * 报名条件
     */
    @TableField("join_requires")
    @ApiModelProperty(value = "报名条件")
    @PropCompare(name = "报名条件")
    private String joinRequires;

    /**
     * 服务内容
     */
    @TableField("services")
    @ApiModelProperty(value = "服务内容")
    @PropCompare(name = "服务内容")
    private String services;

    /**
     * 团队LOGO
     */
    @TableField("team_photo")
    @ApiModelProperty(value = "团队LOGO")
    @PropCompare(name = "团队LOGO")
    private String teamPhoto;

    /**
     * 负责人姓名
     */
    @TableField("curator_name")
    @ApiModelProperty(value = "负责人姓名")
    @PropCompare(name = "负责人姓名")
    private String curatorName;

    /**
     * 负责人身份证号码
     */
    @TableField("curator_card")
    @Desensitise
    @ApiModelProperty(value = "负责人身份证号码")
    @PropCompare(name = "负责人身份证号码")
    private String curatorCard;

    /**
     * 负责人联系方式
     */
    @TableField("curator_contact")
    @Desensitise
    @ApiModelProperty(value = "负责人联系方式")
    @PropCompare(name = "负责人联系方式")
    private String curatorContact;

    /**
     * 负责人是否是党员
     */
    @TableField("curator_party_member")
    @ApiModelProperty(value = "负责人是否是党员")
    @PropCompare(name = "负责人是否是党员")
    private Boolean curatorPartyMember;

    /**
     * 负责人单位
     */
    @TableField("curator_department")
    @ApiModelProperty(value = "负责人单位")
    @PropCompare(name = "负责人单位")
    private String curatorDepartment;

    /**
     * 负责人职位
     */
    @TableField("curator_job")
    @ApiModelProperty(value = "负责人职位")
    @PropCompare(name = "负责人职位")
    private String curatorJob;

    /**
     * 管理员姓名
     */
    @TableField("admin_name")
    @ApiModelProperty(value = "管理员姓名")
    @PropCompare(name = "管理员姓名")
    private String adminName;

    /**
     * 管理员身份证号码
     */
    @TableField("admin_card")
    @Desensitise
    @ApiModelProperty(value = "管理员身份证号码")
    @PropCompare(name = "管理员身份证号码")
    private String adminCard;

    /**
     * 管理员联系方式
     */
    @TableField("admin_contact")
    @Desensitise
    @ApiModelProperty(value = "管理员联系方式")
    @PropCompare(name = "管理员联系方式")
    private String adminContact;

    /**
     * 管理员是否是党员
     */
    @TableField("admin_party_member")
    @ApiModelProperty(value = "管理员是否是党员")
    @PropCompare(name = "管理员是否是党员")
    private Boolean adminPartyMember;

    /**
     * 管理员单位
     */
    @TableField("admin_department")
    @ApiModelProperty(value = "管理员单位")
    @PropCompare(name = "管理员单位")
    private String adminDepartment;

    /**
     * 管理员职位
     */
    @TableField("admin_job")
    @ApiModelProperty(value = "管理员职位")
    @PropCompare(name = "管理员职位")
    private String adminJob;

    /**
     * 状态（启用，禁用）
     */
    @TableField("status")
    @ApiModelProperty(value = "状态（启用，禁用）")
    private Boolean status;

    /**
     * 团队成员人数
     */
    @TableField("team_number")
    @ApiModelProperty(value = "团队成员人数")
    private Integer teamNumber;

    /**
     * 团队审核状态
     */
    @TableField("team_status")
    @DictTrans(transTo = "teamStatusText")
    @ApiModelProperty(value = "团队审核状态")
    private String teamStatus;

    /**
     * 团队审核状态名
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "团队审核状态")
    private String teamStatusText;

    /**
     * 审核组织
     */
    @TableField(value = "audit_org_code", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "审核组织")
    private String auditOrgCode;

    /**
     * 审核组织名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "审核组织名称")
    private String auditOrgName;

    /**
     * 注册时间
     */
    @TableField("register_time")
    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate registerTime;

    /**
     * 有无专职财务人员
     */
    @TableField("has_financial")
    @ApiModelProperty(value = "有无专职财务人员")
    private Boolean hasFinancial;

    /**
     * 协会重点
     */
    @TableField("is_emphasis")
    @DictTrans(transTo = "emphasisText")
    @ApiModelProperty(value = "协会重点")
    private String isEmphasis;

    /**
     * 协会重点Text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "协会重点Text")
    private String emphasisText;

    /**
     * 是否同步
     */
    @DictTrans(transTo = "isSyncText")
    @TableField("is_sync")
    @ApiModelProperty(value = "是否同步")
    private String isSync;

    /**
     * 是否同步Text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否同步Text")
    private String isSyncText;

    /**
     * 同步备注
     */
    @TableField("sync_remark")
    @ApiModelProperty(value = "同步备注")
    private String syncRemark;

    /**
     * 同步时间
     */
    @TableField("sync_time")
    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime syncTime;

    /**
     * 同步成功后市区返回的Id
     */
    @TableField("sync_id")
    @ApiModelProperty(value = "同步成功后市区返回的Id")
    private String syncId;

    /**
     * 团队编号
     */
    @TableField("team_no")
    @ApiModelProperty(value = "团队编号")
    private String teamNo;

    /**
     * 是否活跃
     */
    @TableField("is_lively")
    @ApiModelProperty(value = "是否活跃")
    private Boolean lively;

    /**
     * 当年活跃人数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "当年活跃人数")
    private Integer livelyCountThisYear = 0;

    /**
     * 当年服务时长
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "当年服务时长")
    private BigDecimal serviceLongThisYear = BigDecimal.ZERO;

    /**
     * 当年活动次数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "当年活动次数")
    private Integer activityCountThisYear = 0;

    /**
     * 团队类型
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "团队类型")
    private String teamType;

    /**
     * 活动集合
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "活动集合")
    private List<ZyzActivity> zyzActivities;

    /**
     * 活动次数
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "活动次数")
    private Integer activityCount = 0;

    /**
     * 服务时长
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong = BigDecimal.ZERO;

    /**
     * 志愿者状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "志愿者状态")
    private Boolean volunteerStatus;

    /**
     * 审核类型（0：团队注册审核；1：团队信息变更审核）
     */
    @TableField("audit_type")
    @ApiModelProperty(value = "审核类型（0：团队注册审核；1：团队信息变更审核）")
    private Integer auditType;

    /**
     * 信息变更后审核状态
     */
    @TableField(value = "info_change_audit_status", updateStrategy = FieldStrategy.IGNORED)
    @DictTrans(transTo = "infoChangeAuditStatusText")
    @ApiModelProperty(value = "信息变更后审核状态")
    private String infoChangeAuditStatus;

    /**
     * 信息变更后审核状态text
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "信息变更后审核状态text")
    private String infoChangeAuditStatusText;

    @TableField(exist = false)
    @ApiModelProperty(value = "信息变更字段信息")
    private List<PropChangeInfo> changePropsInfo;


    /**
     * 经度
     */
    @TableField("longitude")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /**
     * 是否社会工作服务机构
     */
    @TableField("social_org_flag")
    @ApiModelProperty(value = "是否社会工作服务机构")
    private Boolean socialOrgFlag;

    /**
     * 服务对象
     */
    @TableField("service_target")
    @ApiModelProperty(value = "服务对象 社工部字典类型:service_target")
    private String serviceTarget;

    /**
     * 是否成立党组织
     */
    @TableField("party_org_flag")
    @ApiModelProperty(value = "是否成立党组织")
    private Boolean partyOrgFlag;

    /**
     * 团队性质
     */
    @TableField("team_nature")
    @ApiModelProperty(value = "团队性质 系统字典类型:team_nature")
    private String teamNature;

    /**
     * 团队类型
     */
    @TableField("team_classify")
    @ApiModelProperty(value = "团队类型 社工部字典类型:team_classify")
    private String teamClassify;

    /**
     * 信息是否完善
     */
    @TableField("perfect_lag")
    @ApiModelProperty(value = "是否完善")
    private Boolean perfectFlag;

}
