package com.fykj.scaffold.zyz.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.cache.support.DictTrans;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MyTeamDto {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String name;

    /**
     * 团队照片
     */
    @ApiModelProperty(value = "团队照片")
    private String teamPhoto;

    /**
     * 团队联系人
     */
    @ApiModelProperty(value = "团队联系人")
    private String contactPerson;

    /**
     * 团队联系电话
     */
    @ApiModelProperty(value = "团队联系电话")
    private String contactPhone;

    /**
     * 加入时间
     */
    @ApiModelProperty(value = "加入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;

    /**
     * 申请团队状态
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "驳回理由")
    private String remark;

    /**
     * 审核记录id
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "审核记录id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long auditId;
    @TableField(exist = false)
    @DictTrans(transTo = "volunteerStatusText")
    @ApiModelProperty(value = "人员状态")
    private String volunteerStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "人员状态Text")
    private String volunteerStatusText;


    @TableField(exist = false)
    @ApiModelProperty(value = "上级组织")
    private String orgName;
}
