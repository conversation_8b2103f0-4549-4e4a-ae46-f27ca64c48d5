package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动时间段统计结果DTO
 */
@Data
@ApiModel("活动时间段统计结果")
public class ActivityPeriodStatDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("活动ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long activityId;
    
    @ApiModelProperty("时间段ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long timePeriodId;
    
    @ApiModelProperty("活动名称")
    private String activityName;
    
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    @ApiModelProperty("参与人数")
    private Integer participantCount;
    
    @ApiModelProperty("总服务时长")
    private BigDecimal totalServiceLong;
} 