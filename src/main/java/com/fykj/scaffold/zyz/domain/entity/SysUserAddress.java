package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> @email ${email}
 * @date 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_user_address")
public class SysUserAddress extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 省code
     */
    @TableField("province_code")
    @ApiModelProperty(value = "省code")
    private String provinceCode;

    /**
     * 市code
     */
    @TableField("city_code")
    @ApiModelProperty(value = "市code")
    private String cityCode;

    /**
     * 省市区id
     */
    @TableField("area_code")
    @ApiModelProperty(value = "省市区id")
    private String areaCode;

    /**
     * 省份
     */
    @TableField("province_name")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /**
     * 市
     */
    @TableField("city_name")
    @ApiModelProperty(value = "市")
    private String cityName;

    /**
     * 地区
     */
    @TableField("area_name")
    @ApiModelProperty(value = "地区")
    private String areaName;


    /**
     * 详细地址
     */
    @TableField("area_address")
    @ApiModelProperty(value = "详细地址")
    private String areaAddress;

    /**
     * 用户id
     */
    @TableField("user_id")
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 收件人手机号
     */
    @TableField("link_phone")
    @ApiModelProperty(value = "收件人手机号")
    private String linkPhone;

    /**
     * 是否默认地址
     */
    @TableField("tacitly_approve")
    @ApiModelProperty(value = "是否默认地址")
    private Boolean tacitlyApprove;

    /**
     * 性别0男1女
     */
    @TableField("gender")
    @ApiModelProperty(value = "性别0男1女")
    private Boolean gender;

    /**
     * 联系人姓名
     */
    @TableField("user_name")
    @ApiModelProperty(value = "联系人姓名")
    private String userName;

}
