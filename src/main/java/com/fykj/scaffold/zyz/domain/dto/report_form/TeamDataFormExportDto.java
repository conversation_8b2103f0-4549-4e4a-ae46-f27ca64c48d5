package com.fykj.scaffold.zyz.domain.dto.report_form;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 活动报表dto-活动统计
 */
@Data
@NoArgsConstructor
public class TeamDataFormExportDto {

    /**
     * 团队名称
     */
    @ExcelProperty("团队名称")
    private String teamName;

    /**
     * 注册人数
     */
    @ExcelProperty("注册人数")
    private Integer registerNum;

    /**
     * 活动总场次
     */
    @ExcelProperty("活动总场次")
    private Integer actNum;

    /**
     * 活动总人数
     */
    @ExcelProperty("活动总人数")
    private Integer actJoinVolunteerNum;
    /**
     * 群众活动总场次
     */
    @ExcelProperty("群众活动总场次")
    private Integer actNumPublic;

    /**
     * 群众活动总人数
     */
    @ExcelProperty("群众活动总人数")
    private Integer actJoinVolunteerNumPublic;

    /**
     * 活跃人数
     */
    @ExcelProperty("活跃人数")
    private Integer activeNum;

    /**
     * 总时长
     */
    @ExcelProperty("总时长")
    private BigDecimal totalServiceTime;

    /**
     * 发布新闻数
     */
    @ExcelProperty("发布新闻数")
    private Integer publishNewsNum;

    /**
     * 资源总数
     */
    @ExcelProperty("资源总数")
    private Integer resourceNum;

    /**
     * 资源使用数
     */
    @ExcelProperty("资源使用数")
    private Integer resourceDockingNum;

    /**
     * 需求总数
     */
    @ExcelProperty("需求总数")
    private Integer requirementNum;

    /**
     * 需求使用数
     */
    @ExcelProperty("需求使用数")
    private Integer requirementDockingNum;
}
