package com.fykj.scaffold.zyz.domain.params;

import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DataScreenParams extends BaseParams {

    @ApiModelProperty("统计类型：本月(this_month)/本年(this_year)/累计(total)")
    private String sumType = "total";

    @ApiModelProperty("组织code")
    private String orgCode = "top_dept";

    @ApiModelProperty("开始时间")
    private LocalDateTime start;

    @ApiModelProperty("结束时间")
    private LocalDateTime end;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    @ApiModelProperty("需要忽视的组织code")
    private String[] ignoreOrgCodes;

    @ApiModelProperty("需要包括的组织code")
    private String[] containOrgCodes;

    @ApiModelProperty("是否需要分组统计")
    private Boolean needGroupBy = true;

    @ApiModelProperty("文明培育栏目id")
    private String categoryId;
    @ApiModelProperty("年份")
    private String year;
}
