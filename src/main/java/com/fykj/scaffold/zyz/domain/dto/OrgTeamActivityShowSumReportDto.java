package com.fykj.scaffold.zyz.domain.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrgTeamActivityShowSumReportDto {

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 团队id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 活动总场次
     */
    private Integer actNum;

    /**
     * 活动公示上传总场次
     */
    private Integer actShowUploadNum;

    /**
     * 活动公示审核通过总场次
     */
    private Integer actShowUploadPassNum;

    /**
     * 活动公示审核中总场次
     */
    private Integer actShowUploadAuditProcessingNum;

    /**
     * 活动公示审核不通过总场次
     */
    private Integer actShowUploadAuditRejectNum;


}
