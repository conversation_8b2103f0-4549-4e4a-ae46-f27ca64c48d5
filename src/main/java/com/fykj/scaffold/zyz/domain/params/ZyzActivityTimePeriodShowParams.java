package com.fykj.scaffold.zyz.domain.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fykj.scaffold.support.conns.Cons;
import fykj.microservice.core.base.BaseParams;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 活动-时段表-公示信息
 * 查询参数
 *
 * <AUTHOR>
 * @date 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "活动-时段表-公示信息查询参数")
public class ZyzActivityTimePeriodShowParams extends BaseParams {

    private String key;

    private String publishOrgCode;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime startTime;

    @JsonFormat(pattern = Cons.DATETIME_FORMAT)
    @DateTimeFormat(pattern = Cons.DATETIME_FORMAT)
    private LocalDateTime endTime;

    private String auditStatus;

    /**
     * 审核查询参数-本组织code
     */
    private String myOrgCode;

    /**
     * 我的对接查询参数-本团队id
     */
    private Long myTeamId;

    /**
     * 需要我审核的
     */
    private Boolean needAudit;




}
