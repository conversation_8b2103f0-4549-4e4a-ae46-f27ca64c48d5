package com.fykj.scaffold.zyz.domain.dto.block_chain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;



/**
 * 员工
 *
 * <AUTHOR> @email ${email}
 * @date 2022-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("志愿者模型--区块链")
public class VolunteerBlockChainDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 志愿者姓名
     */
    @ApiModelProperty(value = "志愿者姓名")
    private String name;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 服务时长
     */
    @ApiModelProperty(value = "服务时长")
    private BigDecimal serviceLong;


    /**
     * 证件类型（身份证还是护照等等)
     */

    @ApiModelProperty(value = "证件类型（身份证还是护照等等)")
    private String certificateType;


    @ApiModelProperty(value = "证件类型Text")
    private String certificateTypeText;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String certificateId;


    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String phone;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;


    @ApiModelProperty(value = "国家Text")
    private String countryText;


    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * 完整地址
     */
    @ApiModelProperty(value = "完整地址")
    private String fullAddress;


    /**
     * 民族
     */
    @ApiModelProperty(value = "民族")
    private String nation;
    @ApiModelProperty(value = "民族")
    private String nationText;

    /**
     * 学校
     */
    @ApiModelProperty(value = "学校")
    private String school;

    /**
     * 教育程度
     */
    @ApiModelProperty(value = "教育程度")
    private String education;

    @ApiModelProperty(value = "教育程度")
    private String educationText;

    /**
     * 行业类别
     */
    @ApiModelProperty(value = "行业类别")
    private String industryCate;

    @ApiModelProperty(value = "行业类别Text")
    private String industryCateText;


    /**
     * 专业技能
     */
    @ApiModelProperty(value = "专业技能")
    private String skill;


    @ApiModelProperty(value = "专业技能Text")
    private String skillText;


}
   

