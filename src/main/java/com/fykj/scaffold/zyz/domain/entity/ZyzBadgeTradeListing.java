package com.fykj.scaffold.zyz.domain.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import fykj.microservice.core.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.fykj.scaffold.support.conns.Cons.DATETIME_FORMAT;

/**
 * 勋章交易发布-实体类
 *
 * @date 2025-05-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("zyz_badge_trade_listing")
public class ZyzBadgeTradeListing extends BaseEntity  {

	private static final long serialVersionUID = 5415400109854772451L;

	/**
	 * 勋章ID
	 */
	@TableField("badge_id")
    @ApiModelProperty(value = "勋章ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long badgeId;

	/**
	 * 勋章实例ID
	 */
	@TableField("badge_instance_id")
	@ApiModelProperty(value = "勋章实例ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long badgeInstanceId;

	/**
	 * 卖方用户ID
	 */
	@TableField("seller_id")
    @ApiModelProperty(value = "卖方用户ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long sellerId;

	/**
	 * 卖方用户名称
	 */
	@TableField("seller_name")
	@ApiModelProperty(value = "卖方用户名称")
	private String sellerName;

	/**
	 * 费率前积分
	 */
	@TableField("points_before_fee")
    @ApiModelProperty(value = "费率前积分")
	private Integer pointsBeforeFee;

	/**
	 * 费率后积分
	 */
	@TableField("points_after_fee")
    @ApiModelProperty(value = "费率后积分")
	private Integer pointsAfterFee;

	/**
	 * 平台费率(%)
	 */
	@TableField("platform_fee_rate")
    @ApiModelProperty(value = "平台费率(%)")
	private Integer platformFeeRate;

	/**
	 * 状态(0:发布中,1:已成交,2:已取消)
	 */
	@TableField("status")
    @ApiModelProperty(value = "状态(0:发布中,1:已成交,2:已取消)")
	private Integer status;

	/**
	 * 买方用户ID
	 */
	@TableField("buyer_id")
    @ApiModelProperty(value = "买方用户ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long buyerId;

	/**
	 * 买方用户名称
	 */
	@TableField("buyer_name")
	@ApiModelProperty(value = "买方用户名称")
	private String buyerName;

	/**
	 * 成交时间
	 */
	@TableField("complete_date")
    @ApiModelProperty(value = "成交时间")
	@JsonFormat(pattern = DATETIME_FORMAT)
	@DateTimeFormat(pattern = DATETIME_FORMAT)
	private LocalDateTime completeDate;

	/**
	 * 取消时间
	 */
	@TableField("cancel_date")
    @ApiModelProperty(value = "取消时间")
	@JsonFormat(pattern = DATETIME_FORMAT)
	@DateTimeFormat(pattern = DATETIME_FORMAT)
	private LocalDateTime cancelDate;

	/**
	 * 来源标识（0:自由市场交易,1:平台勋章）
	 */
	@TableField("from_platform")
    @ApiModelProperty(value = "来源标识（false:自由市场交易,true:平台勋章）")
	private Boolean fromPlatform;

	/**
	 * 勋章名称
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "勋章名称")
	private String name;

}
