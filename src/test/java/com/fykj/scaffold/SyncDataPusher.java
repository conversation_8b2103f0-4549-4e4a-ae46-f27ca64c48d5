package com.fykj.scaffold;

import cn.hutool.core.io.FileUtil;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.COMMA;

public class SyncDataPusher {

    private static final String URL_TEMPLATE = "https://zyz.sipac.gov.cn/volunteer_api/admin/sgb/push/syncTeamVolunteer?volunteerTeamId=%s";
    private static final String AUTH_TOKEN = "bearer 5c69f9ee-7833-4ded-bda1-5a85e933a049";
    private static final String COOKIE = "JSESSIONID=D3D6B99B43A4A24322CE468BC1056A42";

    public static void main(String[] args) {
        // 输入的 teamId 字符串
        String input;
        input = FileUtil.readUtf8String("/Users/<USER>/Documents/fykj/zyz/团队成员id.txt");
        List<String> teamIds = Arrays.asList(input.split(","));

        // 每秒最多1次请求（每次10个ID）
        RateLimiter limiter = RateLimiter.create(2.0);

        // 将teamId分批（每10个为一组）
        List<List<String>> batches = splitList(teamIds, 10);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            for (List<String> batch : batches) {
                limiter.acquire();  // 限速: 每秒1次
                String joinedIds = batch.stream().map(str -> str.replaceAll("[\\r\\n]", "")).collect(Collectors.joining(COMMA));
                String url = String.format(URL_TEMPLATE, joinedIds);
                HttpPost post = new HttpPost(url);

                // 设置头部
                post.setHeader("Authorization", AUTH_TOKEN);
                post.setHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)");
                post.setHeader("Accept", "*/*");
                post.setHeader("Host", "zyz.sipac.gov.cn");
                post.setHeader("Connection", "keep-alive");
                post.setHeader("Cookie", COOKIE);

                // 执行请求
                try (CloseableHttpResponse response = httpClient.execute(post)) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(entity.getContent(), StandardCharsets.UTF_8))) {
                            System.out.println("Response for batch " + joinedIds + ":");
                            String line;
                            while ((line = reader.readLine()) != null) {
                                System.out.println(line);
                            }
                        }
                    }
                    Thread.sleep(1000);
                } catch (Exception e) {
                    System.err.println("Error for batch " + joinedIds + ": " + e.getMessage());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 将列表按size分割
    private static List<List<String>> splitList(List<String> list, int size) {
        List<List<String>> parts = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            parts.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return parts;
    }

}
