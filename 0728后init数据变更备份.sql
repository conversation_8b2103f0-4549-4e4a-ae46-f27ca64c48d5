-- 已在init库中跑过，备份误删
-- 新增活动补录专员角色、补录按钮、补录权限
INSERT INTO `sys_role` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `name`, `sequence`, `status`, `role_linked`) VALUES (1684798761477013505, 0, b'0', '2023-07-28 13:31:41', '2023-07-28 13:31:41', 1207590691320098817, 1207590691320098817, 'ACT_BULU_MANAGER', '活动补录专员', 99, 1, NULL);
INSERT INTO `sys_action` (`id`, `action_code`, `action_name`, `action_desc`, `menu_id`, `priority`, `status`, `is_persist`, `service_id`, `bind_api_id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`) VALUES (1684794264642060289, 'zyz:activity:bulu', '补录', '补录', 1632945380145700865, 2, b'1', b'0', '', NULL, 1, b'0', '2023-07-28 13:13:49', '2023-07-28 13:30:05', 1207590691320098817, 1207590691320098817);
INSERT INTO `sys_role_action` (`id`, `action_id`, `role_id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`) VALUES (1684798761544122370, 1684794264642060289, 1684798761477013505, 0, b'0', '2023-07-28 13:31:41', '2023-07-28 13:31:41', 1207590691320098817, 1207590691320098817);

-- 新增修改定位距离按钮
INSERT INTO `sys_action` (`id`, `action_code`, `action_name`, `action_desc`, `menu_id`, `priority`, `status`, `is_persist`, `service_id`, `bind_api_id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`) VALUES (1686921964387196929, 'zyz:activity:sign_distance_change', '定位距离', '定位距离', 1632945380145700865, 3, b'1', b'0', '', NULL, 0, b'0', '2023-08-03 10:08:32', '2023-08-03 10:08:32', 1207590691320098817, 1207590691320098817);
INSERT INTO `sys_role_action` (`id`, `action_id`, `role_id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`) VALUES (1686922218683654145, 1686921964387196929, 1619215311720075265, 0, b'0', '2023-08-03 10:09:33', '2023-08-03 10:09:33', 1207590691320098817, 1207590691320098817);

-- 增加活动操作类型数据字典
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1686931455676092418, 0, b'0', '2023-08-03 10:46:15', '2023-08-03 10:46:15', 1207590691320098817, 1207590691320098817, 'act_sign_distance_change', NULL, '修改签到距离', 10, 1, 'act_sign_distance_change', 1635108867516293122);
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1684807959560519682, 0, b'0', '2023-07-28 14:08:14', '2023-07-28 14:08:14', 1207590691320098817, 1207590691320098817, 'rt_masses', NULL, '群众参与', 1, 1, 'rt_masses', 1684807582631002114);
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1684807769743097858, 0, b'0', '2023-07-28 14:07:29', '2023-07-28 14:07:29', 1207590691320098817, 1207590691320098817, 'rt_volunteer', NULL, '志愿招募', 0, 1, 'rt_volunteer', 1684807582631002114);
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1684807582631002114, 0, b'0', '2023-07-28 14:06:45', '2023-07-28 14:06:45', 1207590691320098817, 1207590691320098817, 'recruitment_targets', NULL, '招募对象', 100, 1, 'recruitment_targets', NULL);
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1686313151997861889, 0, b'0', '2023-08-01 17:49:20', '2023-08-01 17:49:20', 1207590691320098817, 1207590691320098817, 'MTM_ACT_APPLY', NULL, '活动报名', 0, 1, 'MTM_ACT_APPLY', 1648587145406951425);

-- 修改需求、资源类型顺序
UPDATE `sys_dict` SET `version` = 1, `is_deleted` = b'0', `create_date` = '2023-02-23 14:19:08', `update_date` = '2023-08-03 11:15:09', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `code` = 'RT_specialist', `code_prefix` = NULL, `name` = '专家', `sequence` = 2, `status` = 1, `value` = 'RT_specialist', `parent_id` = 1628639939314495490 WHERE `id` = 1628640580703268866;
UPDATE `sys_dict` SET `version` = 1, `is_deleted` = b'0', `create_date` = '2023-02-23 14:19:53', `update_date` = '2023-08-03 11:14:58', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `code` = 'RT_team', `code_prefix` = NULL, `name` = '团队', `sequence` = 1, `status` = 1, `value` = 'RT_team', `parent_id` = 1628639939314495490 WHERE `id` = 1628640768859746306;
UPDATE `sys_dict` SET `version` = 1, `is_deleted` = b'0', `create_date` = '2023-02-23 14:19:30', `update_date` = '2023-08-03 11:14:28', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `code` = 'RT_pavilion', `code_prefix` = NULL, `name` = '场馆', `sequence` = 0, `status` = 1, `value` = 'RT_pavilion', `parent_id` = 1628639939314495490 WHERE `id` = 1628640675490344962;
UPDATE `sys_dict` SET `version` = 1, `is_deleted` = b'0', `create_date` = '2023-02-23 14:18:26', `update_date` = '2023-08-03 11:14:05', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `code` = 'RT_project', `code_prefix` = NULL, `name` = '项目', `sequence` = 3, `status` = 1, `value` = 'RT_project', `parent_id` = 1628639939314495490 WHERE `id` = 1628640406429937666;

-- 工作台增加活动报名管理，项目结项审核，项目审核，后台新增活动非志愿者报名*******如果需要固化icon要定一下******************
INSERT INTO `sys_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `name`, `sequence`, `parent_id`, `url`, `type`, `path`, `icon`, `icon_picture`, `status`, `is_persist`, `open_target`) VALUES (1687014946360619010, 0, b'0', '2023-08-03 16:18:01', '2023-08-03 16:18:01', 1207590691320098817, 1207590691320098817, NULL, '活动报名管理', 4, 1670612414668316674, '/pages_sub1/pages/worktile/massRegister/list', 'app', NULL, NULL, '/vpath/data/volunteer/system/resource/活动报名管理.png', b'1', b'0', '_self');
INSERT INTO `sys_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `name`, `sequence`, `parent_id`, `url`, `type`, `path`, `icon`, `icon_picture`, `status`, `is_persist`, `open_target`) VALUES (1686244380155207681, 2, b'0', '2023-08-01 13:16:04', '2023-08-01 13:17:40', 1207590691320098817, 1207590691320098817, NULL, '群众报名信息', 3, 1622875080913719298, '/activity/activity-apply-public', 'admin', NULL, 'resource-appointment-audit', '', b'1', b'0', '_self');
INSERT INTO `sys_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `name`, `sequence`, `parent_id`, `url`, `type`, `path`, `icon`, `icon_picture`, `status`, `is_persist`, `open_target`) VALUES (1684806062243405825, 4, b'0', '2023-07-28 14:00:42', '2023-07-28 14:00:42', 1207590691320098817, 1207590691320098817, NULL, '结项审核', 49, 1665963903377715202, '/pages_sub1/pages/worktile/projectClose/list', 'app', NULL, NULL, '/vpath/data/volunteer/system/resource/结项审核.png', b'1', b'0', '_self');
INSERT INTO `sys_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `name`, `sequence`, `parent_id`, `url`, `type`, `path`, `icon`, `icon_picture`, `status`, `is_persist`, `open_target`) VALUES (1684805858442174465, 4, b'0', '2023-07-28 13:59:53', '2023-07-28 14:00:05', 1207590691320098817, 1207590691320098817, NULL, '申报审核', 48, 1665963903377715202, '/pages_sub1/pages/worktile/projectApply/list', 'app', NULL, NULL, '/vpath/data/volunteer/system/resource/申报审核.png', b'1', b'0', '_self');

-- 活动报名管理角色权限
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015170399367171, 0, b'0', '2023-08-03 16:18:55', '2023-08-03 16:18:55', '1207590691320098817', '1207590691320098817', 1619215311720075265, 1687014946360619010, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015133720178691, 0, b'0', '2023-08-03 16:18:46', '2023-08-03 16:18:46', '1207590691320098817', '1207590691320098817', 1619215621280681985, 1687014946360619010, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015089076006913, 0, b'0', '2023-08-03 16:18:35', '2023-08-03 16:18:35', '1207590691320098817', '1207590691320098817', 1619215689857552385, 1687014946360619010, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015026195001347, 0, b'0', '2023-08-03 16:18:20', '2023-08-03 16:18:20', '1207590691320098817', '1207590691320098817', 1619216372472139778, 1687014946360619010, NULL, NULL, NULL, NULL);

-- 群众报名权限审核
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687019727590354945, 0, b'0', '2023-08-03 16:37:01', '2023-08-03 16:37:01', '1207590691320098817', '1207590691320098817', 1619215311720075265, 1686244380155207681, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687019705054359559, 0, b'0', '2023-08-03 16:36:56', '2023-08-03 16:36:56', '1207590691320098817', '1207590691320098817', 1619215621280681985, 1686244380155207681, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687019679972421634, 0, b'0', '2023-08-03 16:36:50', '2023-08-03 16:36:50', '1207590691320098817', '1207590691320098817', 1619215689857552385, 1686244380155207681, NULL, NULL, NULL, NULL);
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687019645130338307, 0, b'0', '2023-08-03 16:36:41', '2023-08-03 16:36:41', '1207590691320098817', '1207590691320098817', 1619216372472139778, 1686244380155207681, NULL, NULL, NULL, NULL);

-- 结项审核管理角色权限
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015170407755780, 0, b'0', '2023-08-03 16:18:55', '2023-08-03 16:18:55', '1207590691320098817', '1207590691320098817', 1619215311720075265, 1684806062243405825, NULL, NULL, NULL, NULL);

-- 申报审核管理角色权限
INSERT INTO `sys_role_resource` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `role_id`, `resource_id`, `role_name`, `resource_name`, `resource_url`, `is_half_checked`) VALUES (1687015170407755779, 0, b'0', '2023-08-03 16:18:55', '2023-08-03 16:18:55', '1207590691320098817', '1207590691320098817', 1619215311720075265, 1684805858442174465, NULL, NULL, NULL, NULL);

-- 增加短信发送白名单数据字典
INSERT INTO `sys_dict` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `code`, `code_prefix`, `name`, `sequence`, `status`, `value`, `parent_id`) VALUES (1688721295532359682, 0, b'0', '2023-08-08 09:18:26', '2023-08-08 09:18:26', 1207590691320098817, 1207590691320098817, 'MSG_SEND_WHITE_LIST', NULL, '信息发送白名单', 0, 1, 'MSG_SEND_WHITE_LIST', NULL);

-- 修改短信发送白名单数据字典
UPDATE `sys_dict` SET `version` = 1, `is_deleted` = b'0', `create_date` = '2023-08-08 09:18:26', `update_date` = '2023-08-17 10:26:51', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `code` = 'MSG_SEND_WHITE_LIST', `code_prefix` = NULL, `name` = '信息发送白名单', `sequence` = 0, `status` = 1, `value` = '17605530679,15190049162', `parent_id` = NULL WHERE `id` = 1688721295532359682;


-- 修改群众报名信息发送模板
INSERT INTO `message_template` (`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `module`, `code`, `title`, `template`, `template_params_num`, `msg_prompt`, `prompt_params_num`, `description`, `push_way`, `status`, `jump_link`, `link_params_num`) VALUES (1698597879595683841, 1, b'0', '2023-09-04 15:24:28', '2023-09-04 15:25:02', 1207590691320098817, 1207590691320098817, 'MTM_ACT_APPLY', 'ACT_APPLY_NOTICE_PUBLIC', '通知活动发布人查看群众报名详情', '您创建{}即将开始，请即时查看群众报名详情', 1, NULL, 0, NULL, 'sms', b'1', NULL, 0);
UPDATE `message_template` SET `version` = 7, `is_deleted` = b'0', `create_date` = '2023-08-01 18:05:49', `update_date` = '2023-09-04 15:39:45', `creator` = 1207590691320098817, `updater` = 1207590691320098817, `module` = 'MTM_ACT_APPLY', `code` = 'ACT_APPLY_NOTICE', `title` = '有人报名/取消报名通知活动发布人', `template` = '您好，你发布的活动有群众{}，请及时查看', `template_params_num` = 1, `msg_prompt` = '{},{}', `prompt_params_num` = 2, `description` = NULL, `push_way` = 'wechat', `status` = b'1', `jump_link` = '/pages_sub1/pages/worktile/massRegister/list', `link_params_num` = 0 WHERE `id` = 1686317300130963457;

