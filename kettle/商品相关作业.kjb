<?xml version="1.0" encoding="UTF-8"?>
<job>
  <name>商品相关作业</name>
  <description/>
  <extended_description/>
  <job_version/>
  <directory>/</directory>
  <created_user>-</created_user>
  <created_date>2023/05/12 10:48:26.347</created_date>
  <modified_user>-</modified_user>
  <modified_date>2023/05/12 10:48:26.347</modified_date>
  <parameters>
    </parameters>
  <connection>
    <name>zyzEtl</name>
    <server>************</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>volunteer_2023_etl</database>
    <port>5300</port>
    <username>root</username>
    <password>Encrypted 2bedecaa60dfe878a9314ba508cc2fe8d</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.rewriteBatchedStatements</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCompression</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useServerPrepStmts</code>
        <attribute>false</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>5300</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <slaveservers>
    </slaveservers>
  <job-log-table>
    <connection/>
    <schema/>
    <table/>
    <size_limit_lines/>
    <interval/>
    <timeout_days/>
    <field>
      <id>ID_JOB</id>
      <enabled>Y</enabled>
      <name>ID_JOB</name>
    </field>
    <field>
      <id>CHANNEL_ID</id>
      <enabled>Y</enabled>
      <name>CHANNEL_ID</name>
    </field>
    <field>
      <id>JOBNAME</id>
      <enabled>Y</enabled>
      <name>JOBNAME</name>
    </field>
    <field>
      <id>STATUS</id>
      <enabled>Y</enabled>
      <name>STATUS</name>
    </field>
    <field>
      <id>LINES_READ</id>
      <enabled>Y</enabled>
      <name>LINES_READ</name>
    </field>
    <field>
      <id>LINES_WRITTEN</id>
      <enabled>Y</enabled>
      <name>LINES_WRITTEN</name>
    </field>
    <field>
      <id>LINES_UPDATED</id>
      <enabled>Y</enabled>
      <name>LINES_UPDATED</name>
    </field>
    <field>
      <id>LINES_INPUT</id>
      <enabled>Y</enabled>
      <name>LINES_INPUT</name>
    </field>
    <field>
      <id>LINES_OUTPUT</id>
      <enabled>Y</enabled>
      <name>LINES_OUTPUT</name>
    </field>
    <field>
      <id>LINES_REJECTED</id>
      <enabled>Y</enabled>
      <name>LINES_REJECTED</name>
    </field>
    <field>
      <id>ERRORS</id>
      <enabled>Y</enabled>
      <name>ERRORS</name>
    </field>
    <field>
      <id>STARTDATE</id>
      <enabled>Y</enabled>
      <name>STARTDATE</name>
    </field>
    <field>
      <id>ENDDATE</id>
      <enabled>Y</enabled>
      <name>ENDDATE</name>
    </field>
    <field>
      <id>LOGDATE</id>
      <enabled>Y</enabled>
      <name>LOGDATE</name>
    </field>
    <field>
      <id>DEPDATE</id>
      <enabled>Y</enabled>
      <name>DEPDATE</name>
    </field>
    <field>
      <id>REPLAYDATE</id>
      <enabled>Y</enabled>
      <name>REPLAYDATE</name>
    </field>
    <field>
      <id>LOG_FIELD</id>
      <enabled>Y</enabled>
      <name>LOG_FIELD</name>
    </field>
    <field>
      <id>EXECUTING_SERVER</id>
      <enabled>N</enabled>
      <name>EXECUTING_SERVER</name>
    </field>
    <field>
      <id>EXECUTING_USER</id>
      <enabled>N</enabled>
      <name>EXECUTING_USER</name>
    </field>
    <field>
      <id>START_JOB_ENTRY</id>
      <enabled>N</enabled>
      <name>START_JOB_ENTRY</name>
    </field>
    <field>
      <id>CLIENT</id>
      <enabled>N</enabled>
      <name>CLIENT</name>
    </field>
  </job-log-table>
  <jobentry-log-table>
    <connection/>
    <schema/>
    <table/>
    <timeout_days/>
    <field>
      <id>ID_BATCH</id>
      <enabled>Y</enabled>
      <name>ID_BATCH</name>
    </field>
    <field>
      <id>CHANNEL_ID</id>
      <enabled>Y</enabled>
      <name>CHANNEL_ID</name>
    </field>
    <field>
      <id>LOG_DATE</id>
      <enabled>Y</enabled>
      <name>LOG_DATE</name>
    </field>
    <field>
      <id>JOBNAME</id>
      <enabled>Y</enabled>
      <name>TRANSNAME</name>
    </field>
    <field>
      <id>JOBENTRYNAME</id>
      <enabled>Y</enabled>
      <name>STEPNAME</name>
    </field>
    <field>
      <id>LINES_READ</id>
      <enabled>Y</enabled>
      <name>LINES_READ</name>
    </field>
    <field>
      <id>LINES_WRITTEN</id>
      <enabled>Y</enabled>
      <name>LINES_WRITTEN</name>
    </field>
    <field>
      <id>LINES_UPDATED</id>
      <enabled>Y</enabled>
      <name>LINES_UPDATED</name>
    </field>
    <field>
      <id>LINES_INPUT</id>
      <enabled>Y</enabled>
      <name>LINES_INPUT</name>
    </field>
    <field>
      <id>LINES_OUTPUT</id>
      <enabled>Y</enabled>
      <name>LINES_OUTPUT</name>
    </field>
    <field>
      <id>LINES_REJECTED</id>
      <enabled>Y</enabled>
      <name>LINES_REJECTED</name>
    </field>
    <field>
      <id>ERRORS</id>
      <enabled>Y</enabled>
      <name>ERRORS</name>
    </field>
    <field>
      <id>RESULT</id>
      <enabled>Y</enabled>
      <name>RESULT</name>
    </field>
    <field>
      <id>NR_RESULT_ROWS</id>
      <enabled>Y</enabled>
      <name>NR_RESULT_ROWS</name>
    </field>
    <field>
      <id>NR_RESULT_FILES</id>
      <enabled>Y</enabled>
      <name>NR_RESULT_FILES</name>
    </field>
    <field>
      <id>LOG_FIELD</id>
      <enabled>N</enabled>
      <name>LOG_FIELD</name>
    </field>
    <field>
      <id>COPY_NR</id>
      <enabled>N</enabled>
      <name>COPY_NR</name>
    </field>
  </jobentry-log-table>
  <channel-log-table>
    <connection/>
    <schema/>
    <table/>
    <timeout_days/>
    <field>
      <id>ID_BATCH</id>
      <enabled>Y</enabled>
      <name>ID_BATCH</name>
    </field>
    <field>
      <id>CHANNEL_ID</id>
      <enabled>Y</enabled>
      <name>CHANNEL_ID</name>
    </field>
    <field>
      <id>LOG_DATE</id>
      <enabled>Y</enabled>
      <name>LOG_DATE</name>
    </field>
    <field>
      <id>LOGGING_OBJECT_TYPE</id>
      <enabled>Y</enabled>
      <name>LOGGING_OBJECT_TYPE</name>
    </field>
    <field>
      <id>OBJECT_NAME</id>
      <enabled>Y</enabled>
      <name>OBJECT_NAME</name>
    </field>
    <field>
      <id>OBJECT_COPY</id>
      <enabled>Y</enabled>
      <name>OBJECT_COPY</name>
    </field>
    <field>
      <id>REPOSITORY_DIRECTORY</id>
      <enabled>Y</enabled>
      <name>REPOSITORY_DIRECTORY</name>
    </field>
    <field>
      <id>FILENAME</id>
      <enabled>Y</enabled>
      <name>FILENAME</name>
    </field>
    <field>
      <id>OBJECT_ID</id>
      <enabled>Y</enabled>
      <name>OBJECT_ID</name>
    </field>
    <field>
      <id>OBJECT_REVISION</id>
      <enabled>Y</enabled>
      <name>OBJECT_REVISION</name>
    </field>
    <field>
      <id>PARENT_CHANNEL_ID</id>
      <enabled>Y</enabled>
      <name>PARENT_CHANNEL_ID</name>
    </field>
    <field>
      <id>ROOT_CHANNEL_ID</id>
      <enabled>Y</enabled>
      <name>ROOT_CHANNEL_ID</name>
    </field>
  </channel-log-table>
  <pass_batchid>N</pass_batchid>
  <shared_objects_file/>
  <entries>
    <entry>
      <name>Start</name>
      <description/>
      <type>SPECIAL</type>
      <attributes/>
      <start>Y</start>
      <dummy>N</dummy>
      <repeat>N</repeat>
      <schedulerType>0</schedulerType>
      <intervalSeconds>0</intervalSeconds>
      <intervalMinutes>60</intervalMinutes>
      <hour>12</hour>
      <minutes>0</minutes>
      <weekDay>1</weekDay>
      <DayOfMonth>1</DayOfMonth>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>96</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
    <entry>
      <name>商品导入</name>
      <description/>
      <type>TRANS</type>
      <attributes/>
      <specification_method>filename</specification_method>
      <trans_object_id/>
      <filename>${Internal.Entry.Current.Directory}/mall_goods数据输入.ktr</filename>
      <transname/>
      <arg_from_previous>N</arg_from_previous>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name/>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <run_configuration>Pentaho local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>400</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
    <entry>
      <name>SQL</name>
      <description/>
      <type>SQL</type>
      <attributes/>
      <sql>truncate mall_score_goods;
truncate mall_exchange;
truncate mall_stock;
INSERT INTO `mall_score_goods`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `goods_name`, `goods_desc`, `goods_content`, `goods_logo`, `goods_type`, `start_date`, `end_date`, `goods_count`, `is_hot`, `view_count`, `required_score`, `area_level`, `remark`, `exchange_address`, `longitude`, `latitude`, `goods_brand`, `org_code`, `org_name`, `status`, `link_person_name`, `link_phone`, `uu_id`, `exchange_type`, `offline_score`, `score_goods_type`, `sku`) VALUES (10001, 0, b'0', '2023-05-01 00:00:00', '2023-05-01 00:00:00', 88888, 88888, '积分券25分', '&lt;p>积分券25分&lt;/p>', '&lt;p>积分券25分&lt;/p>', NULL, 1, NULL, '2019-12-31 23:59:00', 0, b'0', NULL, NULL, 2, '苏州工业园区志愿者协会', '苏州工业园区志愿者协会', NULL, NULL, '无', 'top_dept', NULL, b'1', '苏州工业园区志愿者协会', '66686603', NULL,'mall_score_goods_exchange_type_offline',25.00,'mall_score_goods_type_shopping','10001');
INSERT INTO `mall_score_goods`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `goods_name`, `goods_desc`, `goods_content`, `goods_logo`, `goods_type`, `start_date`, `end_date`, `goods_count`, `is_hot`, `view_count`, `required_score`, `area_level`, `remark`, `exchange_address`, `longitude`, `latitude`, `goods_brand`, `org_code`, `org_name`, `status`, `link_person_name`, `link_phone`, `uu_id`, `exchange_type`, `offline_score`, `score_goods_type`, `sku`) VALUES (10002, 0, b'0', '2023-05-01 00:00:00', '2023-05-01 00:00:00', 88888, 88888, '积分券50分', '&lt;p&gt;积分券50分&lt;/p>', '&lt;p>积分券50分&lt;/p>', NULL, 1, NULL, '2019-12-31 23:59:00', 0, b'0', NULL, NULL, 2, '苏州工业园区志愿者协会', '苏州工业园区志愿者协会', NULL, NULL, '无', 'top_dept', NULL, b'0', '苏州工业园区志愿者协会', '66686603', NULL,'mall_score_goods_exchange_type_offline',50.00,'mall_score_goods_type_shopping','10002');
INSERT INTO `mall_score_goods`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `goods_name`, `goods_desc`, `goods_content`, `goods_logo`, `goods_type`, `start_date`, `end_date`, `goods_count`, `is_hot`, `view_count`, `required_score`, `area_level`, `remark`, `exchange_address`, `longitude`, `latitude`, `goods_brand`, `org_code`, `org_name`, `status`, `link_person_name`, `link_phone`, `uu_id`,`exchange_type`,`offline_score`,`score_goods_type`,`sku`) VALUES (10003, 0, b'0', '2023-05-01 00:00:00', '2023-05-01 00:00:00', 88888, 88888, '积分券10分', '&lt;p>积分券10分&lt;/p>', '&lt;p>积分券10分&lt;/p>', NULL, 1, '2018-12-31 23:59:00', '2018-12-31 23:59:00', 0, b'0', NULL, NULL, 2, '苏州工业园区志愿者协会', '苏州工业园区志愿者协会', NULL, NULL, '无', 'top_dept', NULL, b'1', '苏州工业园区志愿者协会', '66686603', NULL,'mall_score_goods_exchange_type_offline',10.00,'mall_score_goods_type_shopping','10003');

INSERT INTO `mall_stock`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `name`, `sku`, `unit_price`, `inventory_quantity`, `inventory_total_quantity`, `description`, `org_code`) VALUES (10001, 0, b'0', '0001-01-01 00:00:00', '2023-01-01 00:00:00', 88888, 88888, '积分券25分', '10001', 0.00, 0, 0, NULL, 'top_dept');
INSERT INTO `mall_stock`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `name`, `sku`, `unit_price`, `inventory_quantity`, `inventory_total_quantity`, `description`, `org_code`) VALUES (10002, 0, b'0', '2019-12-02 10:01:22', '2023-01-01 00:00:00', 88888, 88888, '积分券50分', '10002', 0.00, 0, 0, NULL, 'top_dept');
INSERT INTO `mall_stock`(`id`, `version`, `is_deleted`, `create_date`, `update_date`, `creator`, `updater`, `name`, `sku`, `unit_price`, `inventory_quantity`, `inventory_total_quantity`, `description`, `org_code`) VALUES (10003, 0, b'0', '2023-05-01 00:00:00', '2023-05-01 00:00:00', 88888, 88888, '积分券10分', '10003', 0.00, 0, 0, NULL, 'top_dept');</sql>
      <useVariableSubstitution>F</useVariableSubstitution>
      <sqlfromfile>F</sqlfromfile>
      <sqlfilename/>
      <sendOneStatement>F</sendOneStatement>
      <connection>zyzEtl</connection>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>224</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
    <entry>
      <name>积分兑换记录</name>
      <description/>
      <type>TRANS</type>
      <attributes/>
      <specification_method>filename</specification_method>
      <trans_object_id/>
      <filename>${Internal.Entry.Current.Directory}/商品兑换记录.ktr</filename>
      <transname/>
      <arg_from_previous>N</arg_from_previous>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name/>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <run_configuration>Pentaho local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>512</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
    <entry>
      <name>自助服务机器兑换记录</name>
      <description/>
      <type>TRANS</type>
      <attributes/>
      <specification_method>filename</specification_method>
      <trans_object_id/>
      <filename>${Internal.Entry.Current.Directory}/自助服务机器商品兑换记录.ktr</filename>
      <transname/>
      <arg_from_previous>N</arg_from_previous>
      <params_from_previous>N</params_from_previous>
      <exec_per_row>N</exec_per_row>
      <clear_rows>N</clear_rows>
      <clear_files>N</clear_files>
      <set_logfile>N</set_logfile>
      <logfile/>
      <logext/>
      <add_date>N</add_date>
      <add_time>N</add_time>
      <loglevel>Basic</loglevel>
      <cluster>N</cluster>
      <slave_server_name/>
      <set_append_logfile>N</set_append_logfile>
      <wait_until_finished>Y</wait_until_finished>
      <follow_abort_remote>N</follow_abort_remote>
      <create_parent_folder>N</create_parent_folder>
      <logging_remote_work>N</logging_remote_work>
      <run_configuration>Pentaho local</run_configuration>
      <parameters>
        <pass_all_parameters>Y</pass_all_parameters>
      </parameters>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>640</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
    <entry>
      <name>成功</name>
      <description/>
      <type>SUCCESS</type>
      <attributes/>
      <parallel>N</parallel>
      <draw>Y</draw>
      <nr>0</nr>
      <xloc>736</xloc>
      <yloc>192</yloc>
      <attributes_kjc/>
    </entry>
  </entries>
  <hops>
    <hop>
      <from>Start</from>
      <to>SQL</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>Y</unconditional>
    </hop>
    <hop>
      <from>SQL</from>
      <to>商品导入</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>商品导入</from>
      <to>积分兑换记录</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>积分兑换记录</from>
      <to>自助服务机器兑换记录</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
    <hop>
      <from>自助服务机器兑换记录</from>
      <to>成功</to>
      <from_nr>0</from_nr>
      <to_nr>0</to_nr>
      <enabled>Y</enabled>
      <evaluation>Y</evaluation>
      <unconditional>N</unconditional>
    </hop>
  </hops>
  <notepads>
  </notepads>
  <attributes>
    <group>
      <name>METASTORE.pentaho</name>
      <attribute>
        <key>Default Run Configuration</key>
        <value>{"namespace":"pentaho","id":"Default Run Configuration","name":"Default Run Configuration","description":"Defines a default run configuration","metaStoreName":null}</value>
      </attribute>
    </group>
    <group>
      <name>{"_":"Embedded MetaStore Elements","namespace":"pentaho","type":"Default Run Configuration"}</name>
      <attribute>
        <key>Pentaho local</key>
        <value>{"children":[{"children":[],"id":"server","value":null},{"children":[],"id":"clustered","value":"N"},{"children":[],"id":"name","value":"Pentaho local"},{"children":[],"id":"description","value":null},{"children":[],"id":"pentaho","value":"N"},{"children":[],"id":"readOnly","value":"Y"},{"children":[],"id":"sendResources","value":"N"},{"children":[],"id":"logRemoteExecutionLocally","value":"N"},{"children":[],"id":"remote","value":"N"},{"children":[],"id":"local","value":"Y"},{"children":[],"id":"showTransformations","value":"N"}],"id":"Pentaho local","value":null,"name":"Pentaho local","owner":null,"ownerPermissionsList":[]}</value>
      </attribute>
    </group>
  </attributes>
</job>
